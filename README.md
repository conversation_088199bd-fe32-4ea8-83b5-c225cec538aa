# 王国争霸H5 - 4399小游戏

一个基于HTML5的策略建造类单机小游戏，适合在4399等H5游戏平台发布。

## 游戏特色

- 🏰 **建筑系统** - 建造各种功能建筑，发展你的王国
- ⚔️ **军队系统** - 训练不同类型的军队单位
- 🔬 **科技系统** - 研发科技提升王国实力
- 🛡️ **战斗系统** - 挑战关卡，征战四方
- 📜 **任务系统** - 完成任务获得丰厚奖励
- 💾 **存档系统** - 自动保存游戏进度
- 📱 **响应式设计** - 支持PC和移动端

## 游戏玩法

### 资源管理
- **金币** 💰 - 主要货币，用于建造和训练
- **木材** 🪵 - 建筑材料，伐木场生产
- **石材** 🪨 - 建筑材料，采石场生产  
- **食物** 🌾 - 维持军队，农场生产

### 建筑类型
- **资源建筑** - 农场、伐木场、采石场、金矿
- **军事建筑** - 兵营、射箭场、马厩
- **科技建筑** - 研究院
- **防御建筑** - 城墙、箭塔

### 军队单位
- **步兵** ⚔️ - 基础近战单位
- **弓箭手** 🏹 - 远程攻击单位
- **骑兵** 🐎 - 快速移动单位
- **投石车** 🎯 - 攻城器械

### 科技研发
- **农业技术** - 提升食物产量
- **采矿技术** - 提升资源产量
- **建筑技术** - 减少建造时间
- **军事技术** - 提升军队战斗力

## 技术特点

- **纯前端实现** - 使用原生HTML5 + CSS3 + JavaScript
- **模块化设计** - 清晰的代码结构，易于维护
- **事件驱动** - 完善的事件系统
- **本地存储** - 使用LocalStorage保存游戏数据
- **响应式布局** - 适配不同屏幕尺寸
- **性能优化** - 高效的游戏循环和渲染

## 项目结构

```
kingdom_battle/
├── index.html              # 游戏入口页面
├── css/
│   ├── main.css            # 主样式文件
│   └── ui.css              # UI组件样式
├── js/
│   ├── main.js             # 游戏主逻辑
│   ├── game/
│   │   ├── GameData.js     # 游戏数据配置
│   │   ├── GameEngine.js   # 游戏引擎
│   │   ├── ResourceManager.js # 资源管理
│   │   └── BuildingSystem.js  # 建筑系统
│   └── utils/
│       ├── Utils.js        # 工具函数
│       ├── EventSystem.js  # 事件系统
│       └── SaveSystem.js   # 存档系统
└── README.md               # 项目说明
```

## 快速开始

1. **下载项目文件**
2. **使用Web服务器运行** (推荐)
   ```bash
   # 使用Python简单服务器
   python -m http.server 8000
   
   # 或使用Node.js
   npx serve .
   ```
3. **打开浏览器访问** `http://localhost:8000`

## 游戏操作

### 鼠标操作
- **左键点击** - 选择建筑/单位
- **建造模式** - 点击空地建造建筑

### 键盘快捷键
- **ESC** - 打开/关闭游戏菜单
- **B** - 打开建造面板
- **A** - 打开军队面板
- **T** - 打开科技面板
- **F** - 打开战斗面板
- **Q** - 打开任务面板

## 开发说明

### 添加新建筑
1. 在 `GameData.js` 中添加建筑配置
2. 在 `BuildingSystem.js` 中实现特殊逻辑（如需要）
3. 更新UI显示

### 添加新科技
1. 在 `GameData.js` 中添加科技配置
2. 在 `TechSystem.js` 中实现科技效果
3. 更新相关系统的加成计算

### 自定义样式
- 修改 `css/main.css` 调整主要样式
- 修改 `css/ui.css` 调整UI组件样式

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 性能优化建议

1. **资源优化** - 压缩图片和音频文件
2. **代码优化** - 使用代码压缩工具
3. **缓存策略** - 设置适当的缓存头
4. **CDN加速** - 使用CDN分发静态资源

## 部署到4399

1. **文件准备** - 确保所有文件路径正确
2. **资源检查** - 验证所有资源文件可访问
3. **测试** - 在不同设备和浏览器测试
4. **上传** - 按照4399平台要求上传文件

## 许可证

MIT License - 可自由使用和修改

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础建筑系统
- 实现资源管理系统
- 实现存档系统
- 响应式UI设计

## 联系方式

如有问题或建议，请联系开发者。

---

**王国争霸H5** - 打造属于你的王国！
