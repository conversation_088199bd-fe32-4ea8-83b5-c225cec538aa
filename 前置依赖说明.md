# 前置依赖说明功能

## 功能概述

为了让玩家更清楚地了解为什么某些建筑、军队或科技无法使用，我们在UI中添加了详细的前置依赖说明。

## 修改内容

### 1. 建筑面板改进

**位置**: `js/main.js` - `createBuildingItem` 方法

**新增功能**:
- 显示等级要求: `🔒 需要等级 X (当前: Y)`
- 显示资源不足: `💰 资源不足`
- 显示可建造状态: `✅ 可以建造`

**建筑解锁等级**:
- 🌾 农场: 等级1
- 🪵 伐木场: 等级1
- 🪨 采石场: 等级2
- 💰 金矿: 等级3
- 🏰 兵营: 等级2
- 🏹 射箭场: 等级3
- 🐎 马厩: 等级5
- 🔬 研究院: 等级4
- 🛡️ 城墙: 等级3
- 🗼 箭塔: 等级4

### 2. 军队招募改进

**位置**: `js/main.js` - `loadArmyRecruitTab` 方法

**新增功能**:
- 显示等级要求: `🔒 需要等级 X (当前: Y)`
- 显示资源不足: `💰 资源不足`
- 显示可训练状态: `✅ 可以训练`

**军队解锁等级**:
- ⚔️ 步兵: 等级2
- 🏹 弓箭手: 等级3
- 🐎 骑兵: 等级5
- 🎯 投石车: 等级7

### 3. 科技研发改进

**位置**: `js/main.js` - `loadTechPanel` 方法

**新增功能**:
- 显示等级要求: `🔒 需要等级 X (当前: Y)`
- 显示建筑要求: `🏛️ 需要建造研究院`
- 显示资源不足: `💰 资源不足`
- 显示研发状态: `🔬 研发中...`
- 显示完成状态: `🎓 已研发完成`
- 显示可研发状态: `✅ 可以研发`

**科技解锁等级**:
- 🌾 农业技术: 等级2
- ⛏️ 采矿技术: 等级3
- 🏗️ 建筑技术: 等级3
- ⚔️ 武器锻造: 等级4
- 🛡️ 盔甲制作: 等级4
- 📋 战术学: 等级6

### 4. CSS样式

**位置**: `css/ui.css`

**新增样式类**:
- `.unlock-requirement`: 基础样式
- `.level-locked`: 等级锁定 (黄色)
- `.resource-locked`: 资源不足 (红色)
- `.building-locked`: 建筑要求 (灰色)
- `.unlocked`: 可用状态 (绿色)
- `.researched`: 已研发 (蓝色)
- `.researching`: 研发中 (紫色，带脉冲动画)

## 用户体验改进

1. **清晰的视觉反馈**: 不同颜色表示不同的锁定状态
2. **具体的解锁条件**: 明确告诉玩家需要什么条件
3. **当前状态显示**: 显示玩家当前等级和目标等级
4. **动态更新**: 当条件满足时，状态会自动更新

## 游戏进度指导

现在玩家可以清楚地看到:
- 当前可以做什么
- 需要什么条件才能解锁新内容
- 如何通过升级来获得更多选择

这样玩家就不会困惑为什么只能建造农场和伐木场，而是明确知道需要升级到更高等级才能解锁其他建筑。
