/* 王国争霸H5 - UI组件样式 */

/* 侧边面板 */
.side-panel {
    position: fixed;
    top: 80px;
    right: -400px;
    width: 380px;
    height: calc(100vh - 180px);
    background: rgba(0,0,0,0.95);
    color: white;
    border-radius: 15px 0 0 15px;
    box-shadow: -5px 0 20px rgba(0,0,0,0.5);
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.side-panel.show {
    right: 0;
    box-shadow: -10px 0 30px rgba(0,0,0,0.7);
}

/* 面板遮罩层 */
.panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.3);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.panel-overlay.show {
    opacity: 1;
    visibility: visible;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 15px 0 0 0;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.panel-content {
    padding: 20px;
    height: calc(100% - 80px);
    overflow-y: auto;
}

/* 建造面板 */
.building-categories {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.category-btn {
    padding: 8px 16px;
    background: rgba(255,255,255,0.1);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.category-btn.active,
.category-btn:hover {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    transform: translateY(-1px);
}

.building-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.building-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.building-item:hover {
    background: rgba(255,255,255,0.2);
    border-color: #4CAF50;
    transform: translateY(-2px);
}

.building-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.building-item.disabled:hover {
    transform: none;
    border-color: transparent;
}

.building-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.building-name {
    font-weight: bold;
    color: #FFD700;
}

.building-cost {
    font-size: 0.9rem;
    color: #ccc;
}

.building-desc {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 10px;
}

.building-stats {
    display: flex;
    gap: 15px;
    font-size: 0.8rem;
    color: #aaa;
}

/* 标签页 */
.army-tabs,
.quest-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 20px;
    border-bottom: 2px solid rgba(255,255,255,0.1);
}

.tab-btn {
    padding: 10px 20px;
    background: none;
    color: #ccc;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
    font-size: 1rem;
}

.tab-btn.active,
.tab-btn:hover {
    color: white;
    border-bottom-color: #4CAF50;
}

.tab-content {
    min-height: 300px;
}

/* 军队面板 */
.unit-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.unit-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.unit-info {
    flex: 1;
}

.unit-name {
    font-weight: bold;
    color: #FFD700;
    margin-bottom: 5px;
}

.unit-stats {
    font-size: 0.9rem;
    color: #ccc;
}

.unit-actions {
    display: flex;
    gap: 10px;
}

/* 科技树 */
.tech-tree {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.tech-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.tech-item:hover {
    background: rgba(255,255,255,0.2);
    border-color: #4CAF50;
}

.tech-item.researched {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    border-color: #4CAF50;
}

.tech-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.tech-name {
    font-weight: bold;
    margin-bottom: 10px;
    color: #FFD700;
}

.tech-desc {
    font-size: 0.8rem;
    color: #ccc;
    margin-bottom: 10px;
}

.tech-cost {
    font-size: 0.8rem;
    color: #aaa;
}

/* 战斗面板 */
.battle-modes {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.mode-btn {
    padding: 10px 20px;
    background: rgba(255,255,255,0.1);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-btn.active,
.mode-btn:hover {
    background: linear-gradient(45deg, #FF6B6B, #FF5252);
}

.battle-content {
    min-height: 300px;
}

.battle-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.battle-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.battle-item:hover {
    background: rgba(255,255,255,0.2);
    border-color: #FF6B6B;
}

.battle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.battle-name {
    font-weight: bold;
    color: #FFD700;
}

.battle-difficulty {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.difficulty-easy { background: #4CAF50; }
.difficulty-normal { background: #FF9800; }
.difficulty-hard { background: #F44336; }

/* 任务面板 */
.quest-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.quest-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    border-left: 4px solid #4CAF50;
}

.quest-item.completed {
    border-left-color: #FFD700;
    background: rgba(255, 215, 0, 0.1);
}

.quest-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.quest-name {
    font-weight: bold;
    color: #FFD700;
}

.quest-reward {
    font-size: 0.9rem;
    color: #4CAF50;
}

.quest-desc {
    font-size: 0.9rem;
    color: #ccc;
    margin-bottom: 10px;
}

.quest-progress {
    font-size: 0.8rem;
    color: #aaa;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    transition: opacity 0.3s ease;
}

.modal.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.modal-content h3,
.modal-content h4 {
    margin-bottom: 20px;
    color: #FFD700;
}

.menu-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.option-btn {
    padding: 12px 24px;
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.option-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

.dialog-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
}

.btn-secondary {
    background: #757575;
    color: white;
}

.btn-secondary:hover {
    background: #616161;
}

/* 消息提示 */
.message-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 3000;
    max-width: 300px;
}

.message {
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transform: translateX(100%);
    animation: slideIn 0.3s ease forwards;
}

.message.success {
    border-left: 4px solid #4CAF50;
}

.message.error {
    border-left: 4px solid #F44336;
}

.message.info {
    border-left: 4px solid #2196F3;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .side-panel {
        width: 100%;
        right: -100%;
        top: 70px;
        height: calc(100vh - 150px);
        border-radius: 0;
    }
    
    .panel-header {
        border-radius: 0;
    }
    
    .building-categories,
    .battle-modes {
        gap: 5px;
    }
    
    .category-btn,
    .mode-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    .modal-content {
        padding: 20px;
        margin: 20px;
    }
}
