<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>王国争霸H5 - 4399小游戏</title>
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/ui.css">
</head>
<body>
    <div id="gameContainer">
        <!-- 游戏加载界面 -->
        <div id="loadingScreen" class="screen">
            <div class="loading-content">
                <h1>王国争霸</h1>
                <div class="loading-bar">
                    <div class="loading-progress" id="loadingProgress"></div>
                </div>
                <p id="loadingText">正在加载游戏资源...</p>
            </div>
        </div>

        <!-- 主菜单界面 -->
        <div id="menuScreen" class="screen hidden">
            <div class="menu-content">
                <h1 class="game-title">王国争霸</h1>
                <div class="menu-buttons">
                    <button id="newGameBtn" class="menu-btn">新游戏</button>
                    <button id="continueBtn" class="menu-btn">继续游戏</button>
                    <button id="helpBtn" class="menu-btn">游戏帮助</button>
                </div>
            </div>
        </div>

        <!-- 主游戏界面 -->
        <div id="gameScreen" class="screen hidden">
            <!-- 顶部信息栏 -->
            <div id="topBar" class="top-bar">
                <!-- 资源显示 -->
                <div class="resource-bar">
                    <div class="resource-item">
                        <span class="resource-icon">💰</span>
                        <span id="goldAmount">1000</span>
                    </div>
                    <div class="resource-item">
                        <span class="resource-icon">🪵</span>
                        <span id="woodAmount">500</span>
                    </div>
                    <div class="resource-item">
                        <span class="resource-icon">🪨</span>
                        <span id="stoneAmount">300</span>
                    </div>
                    <div class="resource-item">
                        <span class="resource-icon">🌾</span>
                        <span id="foodAmount">800</span>
                    </div>
                </div>

                <!-- 玩家信息 -->
                <div class="player-info">
                    <div class="level-info">
                        <span>等级: <span id="playerLevel">1</span></span>
                        <div class="exp-bar">
                            <div class="exp-progress" id="expProgress"></div>
                        </div>
                    </div>
                    <div class="population-info">
                        <span>人口: <span id="populationCurrent">10</span>/<span id="populationMax">50</span></span>
                    </div>
                </div>
            </div>

            <!-- 游戏主画布 -->
            <div id="gameArea" class="game-area">
                <canvas id="gameCanvas" width="1000" height="600"></canvas>

                <!-- 建筑信息面板 -->
                <div id="buildingInfo" class="info-panel hidden">
                    <h4 id="buildingName">建筑名称</h4>
                    <p id="buildingDesc">建筑描述</p>
                    <div id="buildingActions" class="building-actions">
                        <!-- 动态生成按钮 -->
                    </div>
                </div>

                <!-- 战斗界面 -->
                <div id="battleScreen" class="battle-screen hidden">
                    <div class="battle-header">
                        <h2 id="battleTitle">战斗进行中</h2>
                        <div id="battleRound">第1回合</div>
                        <button id="closeBattleBtn" class="close-battle-btn">&times;</button>
                    </div>

                    <div class="battle-field">
                        <div class="battle-side player-side">
                            <h3>我方军队</h3>
                            <div id="playerUnits" class="battle-unit-list"></div>
                        </div>

                        <div class="battle-center">
                            <div id="battleLog" class="battle-log">
                                <div class="log-entry">战斗即将开始...</div>
                            </div>
                        </div>

                        <div class="battle-side enemy-side">
                            <h3>敌方军队</h3>
                            <div id="enemyUnits" class="battle-unit-list"></div>
                        </div>
                    </div>

                    <div class="battle-controls">
                        <button id="battleSpeedBtn" class="btn btn-secondary">2x速度</button>
                        <button id="battleSkipBtn" class="btn btn-warning">跳过动画</button>
                        <button id="battleAutoBtn" class="btn btn-info">自动战斗</button>
                    </div>
                </div>
            </div>

            <!-- 底部操作栏 -->
            <div id="actionBar" class="action-bar">
                <button id="buildBtn" class="action-btn" data-panel="building">
                    <span class="btn-icon">🏗️</span>
                    <span>建造</span>
                </button>
                <button id="armyBtn" class="action-btn" data-panel="army">
                    <span class="btn-icon">⚔️</span>
                    <span>军队</span>
                </button>
                <button id="techBtn" class="action-btn" data-panel="tech">
                    <span class="btn-icon">🔬</span>
                    <span>科技</span>
                </button>
                <button id="battleBtn" class="action-btn" data-panel="battle">
                    <span class="btn-icon">🛡️</span>
                    <span>战斗</span>
                </button>
                <button id="questBtn" class="action-btn" data-panel="quest">
                    <span class="btn-icon">📜</span>
                    <span>任务</span>
                </button>
                <button id="menuBtn" class="action-btn">
                    <span class="btn-icon">⚙️</span>
                    <span>菜单</span>
                </button>
            </div>
        </div>

        <!-- 面板遮罩层 -->
        <div id="panelOverlay" class="panel-overlay"></div>

        <!-- 建造面板 -->
        <div id="buildingPanel" class="side-panel hidden">
            <div class="panel-header">
                <h3>建造建筑</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="panel-content">
                <div class="building-categories">
                    <button class="category-btn active" data-category="resource">资源</button>
                    <button class="category-btn" data-category="military">军事</button>
                    <button class="category-btn" data-category="tech">科技</button>
                    <button class="category-btn" data-category="defense">防御</button>
                </div>
                <div class="building-list" id="buildingList">
                    <!-- 动态生成建筑选项 -->
                </div>
            </div>
        </div>

        <!-- 军队面板 -->
        <div id="armyPanel" class="side-panel hidden">
            <div class="panel-header">
                <h3>军队管理</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="panel-content">
                <div class="army-tabs">
                    <button class="tab-btn active" data-tab="recruit">招募</button>
                    <button class="tab-btn" data-tab="manage">管理</button>
                </div>
                <div class="tab-content" id="armyContent">
                    <!-- 动态生成内容 -->
                </div>
            </div>
        </div>

        <!-- 科技面板 -->
        <div id="techPanel" class="side-panel hidden">
            <div class="panel-header">
                <h3>科技研发</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="panel-content">
                <div class="tech-tree" id="techTree">
                    <!-- 动态生成科技树 -->
                </div>
            </div>
        </div>

        <!-- 战斗面板 -->
        <div id="battlePanel" class="side-panel hidden">
            <div class="panel-header">
                <h3>战斗</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="panel-content">
                <div class="battle-modes">
                    <button class="mode-btn active" data-mode="campaign">关卡挑战</button>
                    <button class="mode-btn" data-mode="defense">防御战</button>
                    <button class="mode-btn" data-mode="boss">Boss战</button>
                </div>
                <div class="battle-content" id="battleContent">
                    <!-- 动态生成战斗内容 -->
                </div>
            </div>
        </div>

        <!-- 任务面板 -->
        <div id="questPanel" class="side-panel hidden">
            <div class="panel-header">
                <h3>任务中心</h3>
                <button class="close-btn">&times;</button>
            </div>
            <div class="panel-content">
                <div class="quest-tabs">
                    <button class="tab-btn active" data-tab="main">主线</button>
                    <button class="tab-btn" data-tab="daily">日常</button>
                    <button class="tab-btn" data-tab="achievement">成就</button>
                </div>
                <div class="tab-content" id="questContent">
                    <!-- 动态生成任务内容 -->
                </div>
            </div>
        </div>

        <!-- 游戏菜单 -->
        <div id="gameMenu" class="modal hidden">
            <div class="modal-content">
                <h3>游戏菜单</h3>
                <div class="menu-options">
                    <button id="saveBtn" class="option-btn">保存游戏</button>
                    <button id="loadBtn" class="option-btn">加载游戏</button>
                    <button id="settingsBtn" class="option-btn">设置</button>
                    <button id="backToMenuBtn" class="option-btn">返回主菜单</button>
                    <button id="resumeBtn" class="option-btn">继续游戏</button>
                </div>
            </div>
        </div>

        <!-- 消息提示 -->
        <div id="messageContainer" class="message-container"></div>

        <!-- 确认对话框 -->
        <div id="confirmDialog" class="modal hidden">
            <div class="modal-content">
                <h4 id="confirmTitle">确认</h4>
                <p id="confirmMessage">确定要执行此操作吗？</p>
                <div class="dialog-buttons">
                    <button id="confirmYes" class="btn btn-primary">确定</button>
                    <button id="confirmNo" class="btn btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 游戏脚本 -->
    <script src="js/utils/Utils.js"></script>
    <script src="js/utils/EventSystem.js"></script>
    <script src="js/utils/SaveSystem.js"></script>
    <script src="js/game/GameData.js"></script>
    <script src="js/game/GameEngine.js"></script>
    <script src="js/game/ResourceManager.js"></script>
    <script src="js/game/BuildingSystem.js"></script>
    <script src="js/game/ArmySystem.js"></script>
    <script src="js/game/TechSystem.js"></script>
    <script src="js/game/BattleSystem.js"></script>
    <script src="js/game/QuestSystem.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
