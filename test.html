<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>王国争霸H5 - 功能测试</h1>
    
    <div class="test-section">
        <h2>脚本加载测试</h2>
        <div id="scriptStatus"></div>
    </div>
    
    <div class="test-section">
        <h2>系统初始化测试</h2>
        <div id="systemStatus"></div>
        <button onclick="testSystems()">测试系统</button>
    </div>
    
    <div class="test-section">
        <h2>UI功能测试</h2>
        <div id="uiStatus"></div>
        <button onclick="testUI()">测试UI</button>
    </div>
    
    <div class="test-section">
        <h2>存档功能测试</h2>
        <div id="saveStatus"></div>
        <button onclick="testSave()">测试存档</button>
    </div>

    <!-- 游戏脚本 -->
    <script src="js/utils/Utils.js"></script>
    <script src="js/utils/EventSystem.js"></script>
    <script src="js/utils/SaveSystem.js"></script>
    <script src="js/game/GameData.js"></script>
    <script src="js/game/GameEngine.js"></script>
    <script src="js/game/ResourceManager.js"></script>
    <script src="js/game/BuildingSystem.js"></script>
    <script src="js/game/ArmySystem.js"></script>
    <script src="js/game/TechSystem.js"></script>
    <script src="js/game/BattleSystem.js"></script>
    <script src="js/game/QuestSystem.js"></script>

    <script>
        // 测试脚本
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML += `<div class="status ${type}">${message}</div>`;
        }

        // 检查脚本加载
        function checkScripts() {
            const scripts = [
                'Utils', 'EventSystem', 'SaveSystem', 'GameData',
                'GameEngine', 'ResourceManager', 'BuildingSystem',
                'ArmySystem', 'TechSystem', 'BattleSystem', 'QuestSystem'
            ];
            
            let loaded = 0;
            scripts.forEach(script => {
                if (window[script]) {
                    updateStatus('scriptStatus', `✓ ${script} 加载成功`, 'success');
                    loaded++;
                } else {
                    updateStatus('scriptStatus', `✗ ${script} 加载失败`, 'error');
                }
            });
            
            updateStatus('scriptStatus', `总计: ${loaded}/${scripts.length} 个脚本加载成功`, loaded === scripts.length ? 'success' : 'error');
        }

        // 测试系统初始化
        function testSystems() {
            updateStatus('systemStatus', '开始测试系统初始化...', 'info');
            
            try {
                // 测试资源管理器
                const resources = new ResourceManager();
                updateStatus('systemStatus', '✓ ResourceManager 初始化成功', 'success');
                
                // 测试建筑系统
                const buildings = new BuildingSystem();
                updateStatus('systemStatus', '✓ BuildingSystem 初始化成功', 'success');
                
                // 测试军队系统
                const army = new ArmySystem();
                updateStatus('systemStatus', '✓ ArmySystem 初始化成功', 'success');
                
                // 测试科技系统
                const tech = new TechSystem();
                updateStatus('systemStatus', '✓ TechSystem 初始化成功', 'success');
                
                // 测试战斗系统
                const battle = new BattleSystem();
                updateStatus('systemStatus', '✓ BattleSystem 初始化成功', 'success');
                
                // 测试任务系统
                const quests = new QuestSystem();
                updateStatus('systemStatus', '✓ QuestSystem 初始化成功', 'success');
                
                updateStatus('systemStatus', '所有系统初始化成功！', 'success');
                
            } catch (error) {
                updateStatus('systemStatus', `✗ 系统初始化失败: ${error.message}`, 'error');
                console.error('System test error:', error);
            }
        }

        // 测试UI功能
        function testUI() {
            updateStatus('uiStatus', '开始测试UI功能...', 'info');
            
            try {
                // 测试工具函数
                const testElement = Utils.createElement('div', 'test-class', 'test content');
                if (testElement && testElement.className === 'test-class') {
                    updateStatus('uiStatus', '✓ Utils.createElement 工作正常', 'success');
                } else {
                    updateStatus('uiStatus', '✗ Utils.createElement 有问题', 'error');
                }
                
                // 测试事件系统
                let eventFired = false;
                GameEvents.on('test_event', () => { eventFired = true; });
                GameEvents.emit('test_event');
                
                if (eventFired) {
                    updateStatus('uiStatus', '✓ 事件系统工作正常', 'success');
                } else {
                    updateStatus('uiStatus', '✗ 事件系统有问题', 'error');
                }
                
                updateStatus('uiStatus', 'UI功能测试完成！', 'success');
                
            } catch (error) {
                updateStatus('uiStatus', `✗ UI测试失败: ${error.message}`, 'error');
                console.error('UI test error:', error);
            }
        }

        // 测试存档功能
        function testSave() {
            updateStatus('saveStatus', '开始测试存档功能...', 'info');
            
            try {
                // 测试存储功能
                const testData = { test: 'data', number: 123 };
                const saved = Utils.setStorage('test_save', testData);
                
                if (saved) {
                    updateStatus('saveStatus', '✓ 数据保存成功', 'success');
                } else {
                    updateStatus('saveStatus', '✗ 数据保存失败', 'error');
                    return;
                }
                
                // 测试读取功能
                const loaded = Utils.getStorage('test_save');
                if (loaded && loaded.test === 'data' && loaded.number === 123) {
                    updateStatus('saveStatus', '✓ 数据读取成功', 'success');
                } else {
                    updateStatus('saveStatus', '✗ 数据读取失败', 'error');
                }
                
                // 清理测试数据
                Utils.removeStorage('test_save');
                updateStatus('saveStatus', '存档功能测试完成！', 'success');
                
            } catch (error) {
                updateStatus('saveStatus', `✗ 存档测试失败: ${error.message}`, 'error');
                console.error('Save test error:', error);
            }
        }

        // 页面加载完成后自动检查脚本
        window.addEventListener('DOMContentLoaded', () => {
            setTimeout(checkScripts, 100);
        });
    </script>
</body>
</html>
