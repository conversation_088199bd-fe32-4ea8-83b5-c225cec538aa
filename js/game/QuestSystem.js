// 王国争霸H5 - 任务系统

class Quest {
    constructor(data) {
        this.id = data.id;
        this.name = data.name;
        this.description = data.description;
        this.type = data.type;
        this.target = data.target;
        this.count = data.count;
        this.rewards = data.rewards;
        this.unlockLevel = data.unlockLevel || 1;
        this.repeatable = data.repeatable || false;
        
        this.progress = 0;
        this.isCompleted = false;
        this.isActive = false;
        this.completedCount = 0; // 对于可重复任务
    }

    // 激活任务
    activate() {
        this.isActive = true;
        this.progress = 0;
        this.isCompleted = false;
        
        GameEvents.emit(GAME_EVENTS.QUEST_START, this);
    }

    // 更新任务进度
    updateProgress(amount = 1) {
        if (!this.isActive || this.isCompleted) {
            return false;
        }
        
        this.progress = Math.min(this.progress + amount, this.count);
        
        GameEvents.emit(GAME_EVENTS.QUEST_UPDATE, {
            quest: this,
            progress: this.progress,
            total: this.count
        });
        
        // 检查是否完成
        if (this.progress >= this.count) {
            this.complete();
        }
        
        return true;
    }

    // 完成任务
    complete() {
        if (this.isCompleted) return false;
        
        this.isCompleted = true;
        this.completedCount++;
        
        // 给予奖励
        this.giveRewards();
        
        GameEvents.emit(GAME_EVENTS.QUEST_COMPLETE, this);
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `任务完成：${this.name}`
        });
        
        // 如果是可重复任务，重置状态
        if (this.repeatable) {
            setTimeout(() => {
                this.reset();
            }, 1000);
        }
        
        return true;
    }

    // 给予奖励
    giveRewards() {
        if (!this.rewards) return;
        
        // 给予资源奖励
        for (const [resource, amount] of Object.entries(this.rewards)) {
            if (resource === 'exp') {
                // 经验奖励
                if (window.Game.gainExp) {
                    window.Game.gainExp(amount);
                }
            } else if (window.Game.resources && window.Game.resources.addResource) {
                // 资源奖励
                window.Game.resources.addResource(resource, amount);
            }
        }
    }

    // 重置任务（用于可重复任务）
    reset() {
        this.progress = 0;
        this.isCompleted = false;
        this.isActive = true;
    }

    // 获取进度百分比
    getProgressPercentage() {
        return this.count > 0 ? (this.progress / this.count) * 100 : 0;
    }

    // 获取任务信息
    getInfo() {
        return {
            id: this.id,
            name: this.name,
            description: this.description,
            type: this.type,
            target: this.target,
            progress: this.progress,
            count: this.count,
            isCompleted: this.isCompleted,
            isActive: this.isActive,
            progressPercentage: this.getProgressPercentage(),
            rewards: this.rewards,
            repeatable: this.repeatable,
            completedCount: this.completedCount
        };
    }
}

class QuestSystem {
    constructor() {
        this.quests = new Map();
        this.activeQuests = new Set();
        this.completedQuests = new Set();
        
        this.init();
    }

    // 初始化任务系统
    init() {
        // 初始化主线任务
        for (const questData of GameData.quests.main) {
            const quest = new Quest(questData);
            this.quests.set(quest.id, quest);
        }
        
        // 初始化日常任务
        for (const questData of GameData.quests.daily) {
            const quest = new Quest(questData);
            this.quests.set(quest.id, quest);
        }
        
        // 设置事件监听
        this.setupEventListeners();
        
        // 激活初始任务
        this.activateInitialQuests();
        
        console.log('Quest system initialized');
    }

    // 设置事件监听
    setupEventListeners() {
        // 监听建筑建造事件
        GameEvents.on(GAME_EVENTS.BUILDING_COMPLETE, (building) => {
            this.checkQuestProgress('build', building.type);
        });
        
        // 监听军队训练事件
        GameEvents.on(GAME_EVENTS.ARMY_TRAIN_COMPLETE, (unit) => {
            this.checkQuestProgress('train', unit.type);
        });
        
        // 监听战斗事件
        GameEvents.on(GAME_EVENTS.BATTLE_WIN, (battleData) => {
            this.checkQuestProgress('battle', battleData.stageId);
        });
        
        // 监听资源收集事件
        GameEvents.on(GAME_EVENTS.RESOURCE_CHANGED, (data) => {
            if (data.amount > 0) {
                this.checkQuestProgress('collect', data.type, data.amount);
            }
        });
        
        // 监听等级提升事件
        GameEvents.on(GAME_EVENTS.LEVEL_UP, (level) => {
            this.checkLevelUnlocks(level);
        });
    }

    // 激活初始任务
    activateInitialQuests() {
        // 激活符合等级要求的任务
        for (const quest of this.quests.values()) {
            if (quest.unlockLevel <= window.Game.player.level && !quest.isActive && !this.completedQuests.has(quest.id)) {
                this.activateQuest(quest.id);
            }
        }
    }

    // 激活任务
    activateQuest(questId) {
        const quest = this.quests.get(questId);
        if (!quest) {
            console.error(`Unknown quest: ${questId}`);
            return false;
        }
        
        if (quest.isActive || (quest.isCompleted && !quest.repeatable)) {
            return false;
        }
        
        quest.activate();
        this.activeQuests.add(questId);
        
        return true;
    }

    // 检查任务进度
    checkQuestProgress(type, target, amount = 1) {
        for (const questId of this.activeQuests) {
            const quest = this.quests.get(questId);
            if (!quest || quest.isCompleted) continue;
            
            // 检查任务类型和目标
            if (quest.type === type && (quest.target === target || quest.target === 'any')) {
                quest.updateProgress(amount);
                
                // 如果任务完成，从活跃任务中移除
                if (quest.isCompleted && !quest.repeatable) {
                    this.activeQuests.delete(questId);
                    this.completedQuests.add(questId);
                }
            }
        }
    }

    // 检查等级解锁
    checkLevelUnlocks(level) {
        for (const quest of this.quests.values()) {
            if (quest.unlockLevel === level && !quest.isActive && !this.completedQuests.has(quest.id)) {
                this.activateQuest(quest.id);
                
                GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                    type: MESSAGE_TYPES.INFO,
                    text: `新任务解锁：${quest.name}`
                });
            }
        }
    }

    // 获取任务
    getQuest(questId) {
        return this.quests.get(questId);
    }

    // 获取所有任务
    getAllQuests() {
        return Array.from(this.quests.values());
    }

    // 获取活跃任务
    getActiveQuests() {
        return Array.from(this.activeQuests).map(id => this.quests.get(id)).filter(Boolean);
    }

    // 获取已完成任务
    getCompletedQuests() {
        return Array.from(this.completedQuests).map(id => this.quests.get(id)).filter(Boolean);
    }

    // 获取主线任务
    getMainQuests() {
        return Array.from(this.quests.values()).filter(quest => 
            GameData.quests.main.some(mainQuest => mainQuest.id === quest.id)
        );
    }

    // 获取日常任务
    getDailyQuests() {
        return Array.from(this.quests.values()).filter(quest => 
            GameData.quests.daily.some(dailyQuest => dailyQuest.id === quest.id)
        );
    }

    // 获取可用任务（符合等级要求且未完成）
    getAvailableQuests() {
        return Array.from(this.quests.values()).filter(quest => 
            quest.unlockLevel <= window.Game.player.level && 
            (!quest.isCompleted || quest.repeatable)
        );
    }

    // 手动完成任务（用于测试或特殊情况）
    forceCompleteQuest(questId) {
        const quest = this.quests.get(questId);
        if (!quest) return false;
        
        quest.progress = quest.count;
        quest.complete();
        
        if (!quest.repeatable) {
            this.activeQuests.delete(questId);
            this.completedQuests.add(questId);
        }
        
        return true;
    }

    // 重置日常任务
    resetDailyQuests() {
        for (const quest of this.getDailyQuests()) {
            if (quest.repeatable) {
                quest.reset();
                this.activeQuests.add(quest.id);
            }
        }
        
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.INFO,
            text: '日常任务已刷新！'
        });
    }

    // 获取任务统计
    getQuestStats() {
        const total = this.quests.size;
        const completed = this.completedQuests.size;
        const active = this.activeQuests.size;
        
        return {
            total,
            completed,
            active,
            completionRate: total > 0 ? (completed / total) * 100 : 0
        };
    }

    // 更新任务系统
    update(deltaTime) {
        // 这里可以添加基于时间的任务逻辑
        // 比如每日任务重置等
    }

    // 重置任务系统
    reset() {
        this.activeQuests.clear();
        this.completedQuests.clear();
        
        // 重置所有任务状态
        for (const quest of this.quests.values()) {
            quest.progress = 0;
            quest.isCompleted = false;
            quest.isActive = false;
            quest.completedCount = 0;
        }
        
        // 重新激活初始任务
        this.activateInitialQuests();
    }

    // 加载任务数据
    loadData(questData) {
        if (questData.activeQuests) {
            this.activeQuests = new Set(questData.activeQuests);
        }
        
        if (questData.completedQuests) {
            this.completedQuests = new Set(questData.completedQuests);
        }
        
        if (questData.questProgress) {
            for (const [questId, progress] of Object.entries(questData.questProgress)) {
                const quest = this.quests.get(questId);
                if (quest) {
                    quest.progress = progress.progress || 0;
                    quest.isCompleted = progress.isCompleted || false;
                    quest.isActive = progress.isActive || false;
                    quest.completedCount = progress.completedCount || 0;
                }
            }
        }
    }

    // 获取保存数据
    getSaveData() {
        const questProgress = {};
        
        for (const [questId, quest] of this.quests) {
            questProgress[questId] = {
                progress: quest.progress,
                isCompleted: quest.isCompleted,
                isActive: quest.isActive,
                completedCount: quest.completedCount
            };
        }
        
        return {
            activeQuests: Array.from(this.activeQuests),
            completedQuests: Array.from(this.completedQuests),
            questProgress
        };
    }
}

// 导出任务系统
window.Quest = Quest;
window.QuestSystem = QuestSystem;
