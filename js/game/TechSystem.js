// 王国争霸H5 - 科技系统

class Technology {
    constructor(id) {
        this.id = id;
        this.data = GameData.technologies[id];
        
        if (!this.data) {
            throw new Error(`Unknown technology: ${id}`);
        }
        
        this.isResearched = false;
        this.isResearching = false;
        this.researchTimeRemaining = 0;
        this.researchProgress = 0;
    }

    // 开始研发
    startResearch() {
        this.isResearching = true;
        this.researchTimeRemaining = this.data.researchTime * 1000;
        this.researchProgress = 0;
        
        GameEvents.emit(GAME_EVENTS.TECH_RESEARCH, this);
    }

    // 更新研发进度
    update(deltaTime) {
        if (this.isResearching) {
            this.researchTimeRemaining -= deltaTime;
            this.researchProgress = 1 - (this.researchTimeRemaining / (this.data.researchTime * 1000));
            
            if (this.researchTimeRemaining <= 0) {
                this.completeResearch();
            }
        }
    }

    // 完成研发
    completeResearch() {
        this.isResearching = false;
        this.isResearched = true;
        this.researchTimeRemaining = 0;
        this.researchProgress = 1;
        
        GameEvents.emit(GAME_EVENTS.TECH_COMPLETE, this);
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `${this.data.name}研发完成！`
        });
    }

    // 获取科技信息
    getInfo() {
        return {
            id: this.id,
            name: this.data.name,
            description: this.data.description,
            isResearched: this.isResearched,
            isResearching: this.isResearching,
            researchProgress: this.researchProgress,
            researchTimeRemaining: this.researchTimeRemaining,
            cost: this.data.cost,
            effects: this.data.effects
        };
    }
}

class TechSystem {
    constructor() {
        this.technologies = new Map();
        this.researchedTechs = new Set();
        this.currentResearch = null;
        
        this.init();
    }

    // 初始化科技系统
    init() {
        // 初始化所有科技
        for (const techId of Object.keys(GameData.technologies)) {
            const tech = new Technology(techId);
            this.technologies.set(techId, tech);
        }
        
        console.log('Tech system initialized');
    }

    // 更新科技系统
    update(deltaTime) {
        // 更新当前研发的科技
        if (this.currentResearch) {
            this.currentResearch.update(deltaTime);
            
            if (this.currentResearch.isResearched) {
                this.researchedTechs.add(this.currentResearch.id);
                this.currentResearch = null;
                
                // 更新生产效率（如果有相关科技）
                if (window.Game.resources) {
                    window.Game.resources.updateProduction();
                }
            }
        }
    }

    // 研发科技
    researchTech(techId) {
        const tech = this.technologies.get(techId);
        if (!tech) {
            console.error(`Unknown technology: ${techId}`);
            return false;
        }

        // 检查是否已经研发
        if (tech.isResearched) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '该科技已经研发完成！'
            });
            return false;
        }

        // 检查是否正在研发其他科技
        if (this.currentResearch) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '已有科技正在研发中！'
            });
            return false;
        }

        // 检查等级要求
        if (window.Game.player.level < tech.data.unlockLevel) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: `需要等级${tech.data.unlockLevel}才能研发${tech.data.name}！`
            });
            return false;
        }

        // 检查前置科技（如果有）
        if (tech.data.prerequisites) {
            for (const prereq of tech.data.prerequisites) {
                if (!this.isResearched(prereq)) {
                    const prereqTech = this.technologies.get(prereq);
                    GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                        type: MESSAGE_TYPES.WARNING,
                        text: `需要先研发${prereqTech.data.name}！`
                    });
                    return false;
                }
            }
        }

        // 检查是否有研究院
        if (window.Game.buildings) {
            const academyCount = window.Game.buildings.getBuildingCount('academy');
            if (academyCount === 0) {
                GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                    type: MESSAGE_TYPES.WARNING,
                    text: '需要建造研究院才能研发科技！'
                });
                return false;
            }
        }

        // 检查资源
        if (!window.Game.resources.hasResources(tech.data.cost)) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.ERROR,
                text: '资源不足，无法研发科技！'
            });
            return false;
        }

        // 消耗资源
        window.Game.resources.spendResources(tech.data.cost);

        // 开始研发
        tech.startResearch();
        this.currentResearch = tech;

        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.INFO,
            text: `开始研发${tech.data.name}...`
        });

        return true;
    }

    // 取消研发
    cancelResearch() {
        if (!this.currentResearch) {
            return false;
        }

        // 返还部分资源
        const refund = {};
        for (const [resource, amount] of Object.entries(this.currentResearch.data.cost)) {
            refund[resource] = Math.floor(amount * 0.5); // 返还50%
        }
        
        for (const [resource, amount] of Object.entries(refund)) {
            window.Game.resources.addResource(resource, amount);
        }

        // 重置科技状态
        this.currentResearch.isResearching = false;
        this.currentResearch.researchTimeRemaining = 0;
        this.currentResearch.researchProgress = 0;
        
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.INFO,
            text: `取消研发${this.currentResearch.data.name}`
        });

        this.currentResearch = null;
        return true;
    }

    // 检查科技是否已研发
    isResearched(techId) {
        return this.researchedTechs.has(techId);
    }

    // 检查科技是否可研发
    canResearch(techId) {
        const tech = this.technologies.get(techId);
        if (!tech || tech.isResearched || tech.isResearching) {
            return false;
        }

        // 检查等级要求
        if (window.Game.player.level < tech.data.unlockLevel) {
            return false;
        }

        // 检查前置科技
        if (tech.data.prerequisites) {
            for (const prereq of tech.data.prerequisites) {
                if (!this.isResearched(prereq)) {
                    return false;
                }
            }
        }

        // 检查资源
        if (!window.Game.resources.hasResources(tech.data.cost)) {
            return false;
        }

        // 检查是否有研究院
        if (window.Game.buildings) {
            const academyCount = window.Game.buildings.getBuildingCount('academy');
            if (academyCount === 0) {
                return false;
            }
        }

        return true;
    }

    // 获取科技
    getTech(techId) {
        return this.technologies.get(techId);
    }

    // 获取所有科技
    getAllTechs() {
        return Array.from(this.technologies.values());
    }

    // 获取已研发的科技
    getResearchedTechs() {
        return Array.from(this.researchedTechs);
    }

    // 获取可研发的科技
    getAvailableTechs() {
        return Array.from(this.technologies.values()).filter(tech => 
            this.canResearch(tech.id)
        );
    }

    // 获取当前研发的科技
    getCurrentResearch() {
        return this.currentResearch;
    }

    // 获取科技效果
    getTechEffects() {
        const effects = {};
        
        for (const techId of this.researchedTechs) {
            const tech = this.technologies.get(techId);
            if (tech && tech.data.effects) {
                for (const [effect, value] of Object.entries(tech.data.effects)) {
                    if (!effects[effect]) {
                        effects[effect] = 1;
                    }
                    
                    // 累积效果
                    if (typeof value === 'number') {
                        if (value > 1) {
                            // 乘法效果
                            effects[effect] *= value;
                        } else {
                            // 减法效果
                            effects[effect] *= value;
                        }
                    }
                }
            }
        }
        
        return effects;
    }

    // 应用科技效果到数值
    applyTechEffects(baseValue, effectType) {
        const effects = this.getTechEffects();
        return effects[effectType] ? baseValue * effects[effectType] : baseValue;
    }

    // 获取科技树数据（用于UI显示）
    getTechTreeData() {
        const techTree = {};
        
        for (const [techId, tech] of this.technologies) {
            techTree[techId] = {
                ...tech.getInfo(),
                canResearch: this.canResearch(techId),
                prerequisites: tech.data.prerequisites || []
            };
        }
        
        return techTree;
    }

    // 重置科技系统
    reset() {
        this.researchedTechs.clear();
        this.currentResearch = null;
        
        // 重置所有科技状态
        for (const tech of this.technologies.values()) {
            tech.isResearched = false;
            tech.isResearching = false;
            tech.researchTimeRemaining = 0;
            tech.researchProgress = 0;
        }
    }

    // 加载科技数据
    loadData(techData) {
        this.reset();
        
        if (techData.researchedTechs) {
            for (const techId of techData.researchedTechs) {
                this.researchedTechs.add(techId);
                const tech = this.technologies.get(techId);
                if (tech) {
                    tech.isResearched = true;
                }
            }
        }
        
        if (techData.currentResearch) {
            const tech = this.technologies.get(techData.currentResearch.id);
            if (tech) {
                tech.isResearching = true;
                tech.researchTimeRemaining = techData.currentResearch.timeRemaining;
                tech.researchProgress = techData.currentResearch.progress;
                this.currentResearch = tech;
            }
        }
    }

    // 获取保存数据
    getSaveData() {
        const data = {
            researchedTechs: Array.from(this.researchedTechs)
        };
        
        if (this.currentResearch) {
            data.currentResearch = {
                id: this.currentResearch.id,
                timeRemaining: this.currentResearch.researchTimeRemaining,
                progress: this.currentResearch.researchProgress
            };
        }
        
        return data;
    }
}

// 导出科技系统
window.Technology = Technology;
window.TechSystem = TechSystem;
