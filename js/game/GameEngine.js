// 王国争霸H5 - 游戏引擎

class GameEngine {
    constructor() {
        this.isRunning = false;
        this.isPaused = false;
        this.lastTime = 0;
        this.deltaTime = 0;
        this.fps = 60;
        this.frameTime = 1000 / this.fps;
        
        // 游戏状态
        this.gameState = 'loading'; // loading, menu, playing, paused
        
        // 画布和上下文
        this.canvas = null;
        this.ctx = null;
        
        // 游戏时间
        this.gameTime = 0;
        this.playtime = 0;
        
        // 输入管理
        this.input = {
            mouse: { x: 0, y: 0, pressed: false },
            keys: new Set(),
            touches: []
        };
        
        // 渲染层
        this.layers = new Map();
        
        // 初始化
        this.init();
    }

    // 初始化游戏引擎
    init() {
        this.setupCanvas();
        this.setupInput();
        this.setupLayers();
        
        // 监听游戏事件
        GameEvents.on(GAME_EVENTS.GAME_START, this.start.bind(this));
        GameEvents.on(GAME_EVENTS.GAME_PAUSE, this.pause.bind(this));
        GameEvents.on(GAME_EVENTS.GAME_RESUME, this.resume.bind(this));
        
        console.log('Game engine initialized');
    }

    // 设置画布
    setupCanvas() {
        this.canvas = Utils.$('#gameCanvas');
        if (!this.canvas) {
            console.error('Game canvas not found');
            return;
        }
        
        this.ctx = this.canvas.getContext('2d');
        
        // 设置画布大小
        this.resizeCanvas();
        
        // 监听窗口大小变化
        window.addEventListener('resize', Utils.debounce(() => {
            this.resizeCanvas();
        }, 250));
    }

    // 调整画布大小
    resizeCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        
        // 设置画布样式
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        // 重新渲染
        if (this.isRunning) {
            this.render();
        }
    }

    // 设置输入处理
    setupInput() {
        // 鼠标事件
        this.canvas.addEventListener('mousedown', (e) => {
            this.input.mouse.pressed = true;
            this.updateMousePosition(e);
            this.handleClick(this.input.mouse.x, this.input.mouse.y);
        });

        this.canvas.addEventListener('mouseup', (e) => {
            this.input.mouse.pressed = false;
        });

        this.canvas.addEventListener('mousemove', (e) => {
            this.updateMousePosition(e);
        });

        // 触摸事件（移动端）
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            this.updateTouchPosition(touch);
            this.handleClick(this.input.mouse.x, this.input.mouse.y);
        });

        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            const touch = e.touches[0];
            this.updateTouchPosition(touch);
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.input.keys.add(e.code);
        });

        document.addEventListener('keyup', (e) => {
            this.input.keys.delete(e.code);
        });
    }

    // 更新鼠标位置
    updateMousePosition(e) {
        const rect = this.canvas.getBoundingClientRect();
        this.input.mouse.x = e.clientX - rect.left;
        this.input.mouse.y = e.clientY - rect.top;
    }

    // 更新触摸位置
    updateTouchPosition(touch) {
        const rect = this.canvas.getBoundingClientRect();
        this.input.mouse.x = touch.clientX - rect.left;
        this.input.mouse.y = touch.clientY - rect.top;
    }

    // 处理点击事件
    handleClick(x, y) {
        // 检查建筑点击
        if (window.Game && window.Game.buildings) {
            const building = window.Game.buildings.getBuildingAt(x, y);
            if (building) {
                GameEvents.emit(GAME_EVENTS.BUILDING_SELECT, building);
                return;
            }
        }
        
        // 其他点击处理
        GameEvents.emit('canvas_click', { x, y });
    }

    // 设置渲染层
    setupLayers() {
        this.layers.set('background', []);
        this.layers.set('buildings', []);
        this.layers.set('units', []);
        this.layers.set('effects', []);
        this.layers.set('ui', []);
    }

    // 开始游戏循环
    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.isPaused = false;
        this.gameState = 'playing';
        this.lastTime = performance.now();
        
        this.gameLoop();
        console.log('Game engine started');
    }

    // 暂停游戏
    pause() {
        this.isPaused = true;
        this.gameState = 'paused';
        console.log('Game engine paused');
    }

    // 恢复游戏
    resume() {
        this.isPaused = false;
        this.gameState = 'playing';
        this.lastTime = performance.now();
        console.log('Game engine resumed');
    }

    // 停止游戏
    stop() {
        this.isRunning = false;
        this.isPaused = false;
        this.gameState = 'menu';
        console.log('Game engine stopped');
    }

    // 游戏主循环
    gameLoop() {
        if (!this.isRunning) return;

        const currentTime = performance.now();
        this.deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;

        // 限制帧率
        if (this.deltaTime >= this.frameTime) {
            if (!this.isPaused) {
                this.update(this.deltaTime);
                this.gameTime += this.deltaTime;
                this.playtime += this.deltaTime;
            }
            
            this.render();
        }

        requestAnimationFrame(() => this.gameLoop());
    }

    // 更新游戏逻辑
    update(deltaTime) {
        // 更新游戏系统
        if (window.Game) {
            if (window.Game.resources) {
                window.Game.resources.update(deltaTime);
            }
            if (window.Game.buildings) {
                window.Game.buildings.update(deltaTime);
            }
            if (window.Game.army) {
                window.Game.army.update(deltaTime);
            }
            if (window.Game.tech) {
                window.Game.tech.update(deltaTime);
            }
            if (window.Game.quests) {
                window.Game.quests.update(deltaTime);
            }
        }

        // 发送时间更新事件
        GameEvents.emit(GAME_EVENTS.TIME_TICK, deltaTime);
    }

    // 渲染游戏画面
    render() {
        if (!this.ctx) return;

        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制背景
        this.renderBackground();

        // 渲染各层
        this.renderLayer('background');
        this.renderLayer('buildings');
        this.renderLayer('units');
        this.renderLayer('effects');

        // 渲染建筑预览
        this.renderBuildingPreview();

        this.renderLayer('ui');

        // 绘制调试信息（开发模式）
        if (window.DEBUG) {
            this.renderDebugInfo();
        }
    }

    // 渲染背景
    renderBackground() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.canvas.height);
        gradient.addColorStop(0, '#87CEEB');
        gradient.addColorStop(1, '#98FB98');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // 绘制网格（可选）
        this.renderGrid();
    }

    // 渲染网格
    renderGrid() {
        const gridSize = 50;
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        
        // 垂直线
        for (let x = 0; x <= this.canvas.width; x += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        // 水平线
        for (let y = 0; y <= this.canvas.height; y += gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }

    // 渲染指定层
    renderLayer(layerName) {
        const objects = this.layers.get(layerName);
        if (!objects) return;

        for (const obj of objects) {
            if (obj.render && typeof obj.render === 'function') {
                obj.render(this.ctx);
            }
        }
    }

    // 渲染建筑预览
    renderBuildingPreview() {
        if (!window.Game || !window.Game.buildings || !window.Game.buildings.buildMode) {
            return;
        }

        const preview = window.Game.buildings.previewBuilding;
        if (!preview) return;

        const buildingData = GameData.buildings[preview.type];
        if (!buildingData) return;

        // 设置预览样式
        this.ctx.save();

        if (preview.valid) {
            // 可以建造 - 绿色半透明
            this.ctx.fillStyle = 'rgba(0, 255, 0, 0.3)';
            this.ctx.strokeStyle = '#00FF00';
        } else {
            // 不能建造 - 红色半透明
            this.ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
            this.ctx.strokeStyle = '#FF0000';
        }

        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 5]);

        // 绘制预览建筑
        this.ctx.fillRect(preview.x, preview.y, preview.width, preview.height);
        this.ctx.strokeRect(preview.x, preview.y, preview.width, preview.height);

        // 绘制建筑图标
        this.ctx.font = '32px Arial';
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillStyle = preview.valid ? '#00AA00' : '#AA0000';
        this.ctx.fillText(
            buildingData.icon,
            preview.x + preview.width / 2,
            preview.y + preview.height / 2
        );

        this.ctx.restore();
    }

    // 添加对象到渲染层
    addToLayer(layerName, object) {
        const layer = this.layers.get(layerName);
        if (layer && !layer.includes(object)) {
            layer.push(object);
        }
    }

    // 从渲染层移除对象
    removeFromLayer(layerName, object) {
        const layer = this.layers.get(layerName);
        if (layer) {
            const index = layer.indexOf(object);
            if (index > -1) {
                layer.splice(index, 1);
            }
        }
    }

    // 渲染调试信息
    renderDebugInfo() {
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(10, 10, 200, 100);
        
        this.ctx.fillStyle = 'white';
        this.ctx.font = '12px Arial';
        this.ctx.fillText(`FPS: ${Math.round(1000 / this.deltaTime)}`, 20, 30);
        this.ctx.fillText(`Game Time: ${Math.round(this.gameTime / 1000)}s`, 20, 50);
        this.ctx.fillText(`State: ${this.gameState}`, 20, 70);
        this.ctx.fillText(`Mouse: ${this.input.mouse.x}, ${this.input.mouse.y}`, 20, 90);
    }

    // 获取游戏状态
    getState() {
        return this.gameState;
    }

    // 设置游戏状态
    setState(state) {
        this.gameState = state;
    }

    // 获取画布尺寸
    getCanvasSize() {
        return {
            width: this.canvas.width,
            height: this.canvas.height
        };
    }

    // 获取鼠标位置
    getMousePosition() {
        return { ...this.input.mouse };
    }
}

// 导出游戏引擎
window.GameEngine = GameEngine;
