// 王国争霸H5 - 游戏数据配置

const GameData = {
    // 建筑数据
    buildings: {
        // 资源建筑
        farm: {
            id: 'farm',
            name: '农场',
            category: 'resource',
            description: '生产食物资源',
            icon: '🌾',
            cost: { gold: 100, wood: 50 },
            buildTime: 30,
            produces: { food: 10 },
            productionInterval: 60,
            maxLevel: 10,
            unlockLevel: 1
        },
        lumbermill: {
            id: 'lumbermill',
            name: '伐木场',
            category: 'resource',
            description: '生产木材资源',
            icon: '🪵',
            cost: { gold: 80, stone: 30 },
            buildTime: 25,
            produces: { wood: 8 },
            productionInterval: 60,
            maxLevel: 10,
            unlockLevel: 1
        },
        quarry: {
            id: 'quarry',
            name: '采石场',
            category: 'resource',
            description: '生产石材资源',
            icon: '🪨',
            cost: { gold: 120, wood: 40 },
            buildTime: 35,
            produces: { stone: 6 },
            productionInterval: 60,
            maxLevel: 10,
            unlockLevel: 2
        },
        goldmine: {
            id: 'goldmine',
            name: '金矿',
            category: 'resource',
            description: '生产金币资源',
            icon: '💰',
            cost: { wood: 100, stone: 80 },
            buildTime: 45,
            produces: { gold: 15 },
            productionInterval: 90,
            maxLevel: 8,
            unlockLevel: 3
        },
        
        // 军事建筑
        barracks: {
            id: 'barracks',
            name: '兵营',
            category: 'military',
            description: '训练军队单位',
            icon: '🏰',
            cost: { gold: 200, wood: 100, stone: 50 },
            buildTime: 60,
            maxLevel: 5,
            unlockLevel: 2
        },
        archery: {
            id: 'archery',
            name: '射箭场',
            category: 'military',
            description: '训练弓箭手',
            icon: '🏹',
            cost: { gold: 250, wood: 150, stone: 30 },
            buildTime: 50,
            maxLevel: 5,
            unlockLevel: 3
        },
        stable: {
            id: 'stable',
            name: '马厩',
            category: 'military',
            description: '训练骑兵',
            icon: '🐎',
            cost: { gold: 300, wood: 80, stone: 100 },
            buildTime: 70,
            maxLevel: 5,
            unlockLevel: 5
        },
        
        // 科技建筑
        academy: {
            id: 'academy',
            name: '研究院',
            category: 'tech',
            description: '研发科技升级',
            icon: '🔬',
            cost: { gold: 400, wood: 200, stone: 150 },
            buildTime: 90,
            maxLevel: 3,
            unlockLevel: 4
        },
        
        // 防御建筑
        wall: {
            id: 'wall',
            name: '城墙',
            category: 'defense',
            description: '提升防御能力',
            icon: '🛡️',
            cost: { gold: 150, stone: 200 },
            buildTime: 40,
            defense: 50,
            maxLevel: 8,
            unlockLevel: 3
        },
        tower: {
            id: 'tower',
            name: '箭塔',
            category: 'defense',
            description: '远程防御建筑',
            icon: '🗼',
            cost: { gold: 180, wood: 60, stone: 120 },
            buildTime: 45,
            defense: 30,
            range: 100,
            maxLevel: 6,
            unlockLevel: 4
        }
    },

    // 军队单位数据
    units: {
        // 训练假人（仅用于战斗，不可训练）
        dummy: {
            id: 'dummy',
            name: '训练假人',
            description: '用于练习的假人',
            icon: '🎯',
            attack: 5,
            defense: 5,
            health: 30,
            speed: 0,
            isEnemy: true // 标记为敌人单位
        },
        infantry: {
            id: 'infantry',
            name: '步兵',
            description: '基础近战单位',
            icon: '⚔️',
            cost: { gold: 50, food: 20 },
            trainTime: 30,
            attack: 25,
            defense: 30,
            health: 100,
            speed: 1,
            population: 1,
            unlockLevel: 2
        },
        archer: {
            id: 'archer',
            name: '弓箭手',
            description: '远程攻击单位',
            icon: '🏹',
            cost: { gold: 60, wood: 30, food: 15 },
            trainTime: 25,
            attack: 35,
            defense: 15,
            health: 80,
            speed: 1.2,
            range: 150,
            population: 1,
            unlockLevel: 3
        },
        cavalry: {
            id: 'cavalry',
            name: '骑兵',
            description: '快速移动单位',
            icon: '🐎',
            cost: { gold: 100, food: 40 },
            trainTime: 45,
            attack: 40,
            defense: 25,
            health: 120,
            speed: 2,
            population: 2,
            unlockLevel: 5
        },
        catapult: {
            id: 'catapult',
            name: '投石车',
            description: '攻城器械',
            icon: '🎯',
            cost: { gold: 200, wood: 150, stone: 100 },
            trainTime: 90,
            attack: 80,
            defense: 10,
            health: 150,
            speed: 0.5,
            siegeBonus: 2,
            population: 3,
            unlockLevel: 7
        }
    },

    // 科技数据
    technologies: {
        agriculture: {
            id: 'agriculture',
            name: '农业技术',
            description: '提升食物产量25%',
            icon: '🌾',
            cost: { gold: 200, food: 100 },
            researchTime: 120,
            effects: { foodProduction: 1.25 },
            unlockLevel: 2
        },
        mining: {
            id: 'mining',
            name: '采矿技术',
            description: '提升石材和金币产量20%',
            icon: '⛏️',
            cost: { gold: 300, stone: 150 },
            researchTime: 150,
            effects: { stoneProduction: 1.2, goldProduction: 1.2 },
            unlockLevel: 3
        },
        construction: {
            id: 'construction',
            name: '建筑技术',
            description: '减少建造时间30%',
            icon: '🏗️',
            cost: { gold: 250, wood: 200 },
            researchTime: 100,
            effects: { buildTime: 0.7 },
            unlockLevel: 3
        },
        weaponsmith: {
            id: 'weaponsmith',
            name: '武器锻造',
            description: '提升军队攻击力15%',
            icon: '⚔️',
            cost: { gold: 400, stone: 200 },
            researchTime: 180,
            effects: { unitAttack: 1.15 },
            unlockLevel: 4
        },
        armor: {
            id: 'armor',
            name: '盔甲制作',
            description: '提升军队防御力20%',
            icon: '🛡️',
            cost: { gold: 350, wood: 150, stone: 100 },
            researchTime: 160,
            effects: { unitDefense: 1.2 },
            unlockLevel: 4
        },
        tactics: {
            id: 'tactics',
            name: '战术学',
            description: '提升军队移动速度25%',
            icon: '📋',
            cost: { gold: 500, food: 200 },
            researchTime: 200,
            effects: { unitSpeed: 1.25 },
            unlockLevel: 6
        }
    },

    // 任务数据
    quests: {
        // 主线任务
        main: [
            {
                id: 'build_first_farm',
                name: '建造第一个农场',
                description: '建造一个农场来生产食物',
                type: 'build',
                target: 'farm',
                count: 1,
                rewards: { gold: 100, exp: 50 },
                unlockLevel: 1
            },
            {
                id: 'build_lumbermill',
                name: '建造伐木场',
                description: '建造一个伐木场来生产木材',
                type: 'build',
                target: 'lumbermill',
                count: 1,
                rewards: { gold: 80, exp: 40 },
                unlockLevel: 1
            },
            {
                id: 'first_battle_tutorial',
                name: '新手训练',
                description: '完成新手训练关卡',
                type: 'battle',
                target: 'stage_0',
                count: 1,
                rewards: { gold: 50, exp: 30 },
                unlockLevel: 1
            },
            {
                id: 'build_quarry',
                name: '建造采石场',
                description: '建造一个采石场来生产石材',
                type: 'build',
                target: 'quarry',
                count: 1,
                rewards: { gold: 120, exp: 60 },
                unlockLevel: 2
            },
            {
                id: 'train_first_unit',
                name: '训练第一个士兵',
                description: '在兵营中训练一个步兵',
                type: 'train',
                target: 'infantry',
                count: 1,
                rewards: { gold: 150, exp: 75 },
                unlockLevel: 2
            },
            {
                id: 'first_battle',
                name: '第一次战斗',
                description: '完成第一场战斗',
                type: 'battle',
                target: 'stage_1',
                count: 1,
                rewards: { gold: 200, exp: 100 },
                unlockLevel: 2
            }
        ],
        
        // 日常任务
        daily: [
            {
                id: 'collect_resources',
                name: '收集资源',
                description: '收集任意资源100单位',
                type: 'collect',
                target: 'any',
                count: 100,
                rewards: { gold: 50, exp: 25 },
                repeatable: true
            },
            {
                id: 'train_units',
                name: '训练军队',
                description: '训练5个军队单位',
                type: 'train',
                target: 'any',
                count: 5,
                rewards: { gold: 80, exp: 40 },
                repeatable: true
            }
        ]
    },

    // 战斗关卡数据
    battles: {
        campaign: [
            {
                id: 'stage_0',
                name: '新手训练',
                description: '与训练假人进行战斗练习',
                difficulty: 'tutorial',
                enemies: [
                    { type: 'dummy', count: 1 }
                ],
                rewards: { gold: 50, exp: 25, food: 25 },
                unlockLevel: 1
            },
            {
                id: 'stage_1',
                name: '野外强盗',
                description: '清除威胁村庄的强盗',
                difficulty: 'easy',
                enemies: [
                    { type: 'infantry', count: 3 }
                ],
                rewards: { gold: 100, exp: 50, food: 50 },
                unlockLevel: 2
            },
            {
                id: 'stage_2',
                name: '山贼营地',
                description: '摧毁山贼的营地',
                difficulty: 'normal',
                enemies: [
                    { type: 'infantry', count: 5 },
                    { type: 'archer', count: 2 }
                ],
                rewards: { gold: 200, exp: 100, wood: 100 },
                unlockLevel: 3
            },
            {
                id: 'stage_3',
                name: '敌军前哨',
                description: '攻占敌军的前哨站',
                difficulty: 'hard',
                enemies: [
                    { type: 'infantry', count: 8 },
                    { type: 'archer', count: 4 },
                    { type: 'cavalry', count: 2 }
                ],
                rewards: { gold: 400, exp: 200, stone: 150 },
                unlockLevel: 5
            }
        ]
    },

    // 等级经验表 (调整为更合理的进度)
    levelExp: [
        0, 100, 180, 280, 400, 550, 720, 920, 1150, 1400, 1700,
        2000, 2350, 2750, 3200, 3700, 4250, 4850, 5500, 6200, 7000
    ],

    // 游戏配置
    config: {
        maxPopulation: 200,
        resourceCap: 10000,
        autoSaveInterval: 60000,
        productionInterval: 60000,
        maxBuildingLevel: 10,
        maxArmySize: 50
    }
};

// 导出游戏数据
window.GameData = GameData;
