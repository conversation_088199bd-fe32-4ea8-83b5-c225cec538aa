// 王国争霸H5 - 战斗系统

class BattleSystem {
    constructor() {
        this.currentBattle = null;
        this.battleHistory = [];
        
        this.init();
    }

    // 初始化战斗系统
    init() {
        console.log('Battle system initialized');
    }

    // 开始战斗
    startBattle(stageId, playerUnits) {
        const stageData = this.getBattleStage(stageId);
        if (!stageData) {
            console.error(`Unknown battle stage: ${stageId}`);
            return false;
        }

        // 特殊处理新手训练关卡
        if (stageId === 'stage_0') {
            // 新手训练不需要军队，直接胜利
            setTimeout(() => {
                this.handleTutorialVictory(stageData);
            }, 1000);
            return true;
        }

        // 检查是否有军队
        if (!playerUnits || playerUnits.length === 0) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '请先选择要出战的军队！'
            });
            return false;
        }

        // 创建战斗实例
        this.currentBattle = {
            stageId: stageId,
            stageData: stageData,
            playerUnits: [...playerUnits],
            enemyUnits: this.createEnemyUnits(stageData.enemies),
            turn: 0,
            isPlayerTurn: true,
            battleLog: []
        };

        GameEvents.emit(GAME_EVENTS.BATTLE_START, this.currentBattle);
        
        // 开始战斗循环
        this.processBattle();
        
        return true;
    }

    // 获取战斗关卡数据
    getBattleStage(stageId) {
        return GameData.battles.campaign.find(stage => stage.id === stageId);
    }

    // 创建敌军单位
    createEnemyUnits(enemyData) {
        const enemies = [];
        
        for (const enemy of enemyData) {
            const unitData = GameData.units[enemy.type];
            if (!unitData) continue;
            
            for (let i = 0; i < enemy.count; i++) {
                enemies.push({
                    id: Utils.generateId(),
                    type: enemy.type,
                    name: unitData.name,
                    health: unitData.health,
                    maxHealth: unitData.health,
                    attack: unitData.attack,
                    defense: unitData.defense,
                    speed: unitData.speed
                });
            }
        }
        
        return enemies;
    }

    // 处理战斗
    processBattle() {
        if (!this.currentBattle) return;

        // 开始回合制战斗
        this.startTurnBasedBattle();
    }

    // 开始回合制战斗
    startTurnBasedBattle() {
        const battle = this.currentBattle;

        // 初始化战斗单位顺序（按速度排序）
        battle.turnOrder = this.calculateTurnOrder();
        battle.currentTurnIndex = 0;
        battle.round = 1;

        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.INFO,
            text: `战斗开始！第${battle.round}回合`
        });

        // 开始第一个回合
        this.processTurn();
    }

    // 计算行动顺序
    calculateTurnOrder() {
        const allUnits = [
            ...this.currentBattle.playerUnits.map(unit => ({...unit, side: 'player'})),
            ...this.currentBattle.enemyUnits.map(unit => ({...unit, side: 'enemy'}))
        ];

        // 按速度排序，速度高的先行动
        return allUnits
            .filter(unit => unit.health > 0)
            .sort((a, b) => {
                const speedA = a.getSpeed ? a.getSpeed() : a.speed;
                const speedB = b.getSpeed ? b.getSpeed() : b.speed;
                return speedB - speedA;
            });
    }

    // 处理单个回合
    processTurn() {
        const battle = this.currentBattle;

        if (!battle.turnOrder || battle.turnOrder.length === 0) {
            this.checkBattleEnd();
            return;
        }

        // 获取当前行动的单位
        const currentUnit = battle.turnOrder[battle.currentTurnIndex];

        if (!currentUnit || currentUnit.health <= 0) {
            this.nextTurn();
            return;
        }

        // 执行行动
        setTimeout(() => {
            this.executeUnitAction(currentUnit);
        }, 800); // 每个行动间隔0.8秒
    }

    // 执行单位行动
    executeUnitAction(unit) {
        const battle = this.currentBattle;

        // 选择目标
        const targets = this.selectTargets(unit);

        if (targets.length === 0) {
            this.nextTurn();
            return;
        }

        // 执行攻击
        const target = targets[0]; // 简化：选择第一个目标
        const damage = this.calculateDamage(unit, target);

        // 应用伤害
        target.health = Math.max(0, target.health - damage);

        // 记录战斗日志
        const attackerName = `${unit.side === 'player' ? '我方' : '敌方'}${unit.name}`;
        const targetName = `${target.side === 'player' ? '我方' : '敌方'}${target.name}`;

        battle.battleLog.push({
            round: battle.round,
            action: 'attack',
            attacker: attackerName,
            target: targetName,
            damage: damage,
            targetHealth: target.health
        });

        // 更新UI
        if (window.Game.ui) {
            window.Game.ui.addBattleLog(`${attackerName}攻击${targetName}，造成${damage}点伤害！`, 'damage');
            window.Game.ui.updateBattleUnits();
        }

        // 检查目标是否死亡
        if (target.health <= 0) {
            if (window.Game.ui) {
                window.Game.ui.addBattleLog(`${targetName}被击败！`, 'info');
            }
        }

        // 下一个回合
        setTimeout(() => {
            this.nextTurn();
        }, 1000);
    }

    // 下一个回合
    nextTurn() {
        const battle = this.currentBattle;

        battle.currentTurnIndex++;

        // 如果所有单位都行动完毕，开始新的回合
        if (battle.currentTurnIndex >= battle.turnOrder.length) {
            battle.round++;
            battle.currentTurnIndex = 0;

            // 重新计算行动顺序（移除死亡单位）
            battle.turnOrder = this.calculateTurnOrder();

            if (battle.turnOrder.length === 0) {
                this.checkBattleEnd();
                return;
            }

            if (window.Game.ui) {
                window.Game.ui.addBattleLog(`第${battle.round}回合开始！`, 'info');
                window.Game.ui.updateBattleRound(battle.round);
            }
        }

        // 检查战斗是否结束
        if (this.checkBattleEnd()) {
            return;
        }

        // 继续下一个单位的回合
        this.processTurn();
    }

    // 选择攻击目标
    selectTargets(unit) {
        const battle = this.currentBattle;
        let enemies;

        if (unit.side === 'player') {
            enemies = battle.turnOrder.filter(u => u.side === 'enemy' && u.health > 0);
        } else {
            enemies = battle.turnOrder.filter(u => u.side === 'player' && u.health > 0);
        }

        // 简化的AI：优先攻击血量最少的敌人
        return enemies.sort((a, b) => a.health - b.health);
    }

    // 计算伤害
    calculateDamage(attacker, target) {
        const attack = attacker.getAttack ? attacker.getAttack() : attacker.attack;
        const defense = target.getDefense ? target.getDefense() : target.defense;

        // 基础伤害计算
        let damage = Math.max(1, attack - defense);

        // 添加随机因子 (80%-120%)
        const randomFactor = 0.8 + Math.random() * 0.4;
        damage = Math.floor(damage * randomFactor);

        // 暴击判定 (10%概率)
        if (Math.random() < 0.1) {
            damage = Math.floor(damage * 1.5);
            if (window.Game.ui) {
                window.Game.ui.addBattleLog('暴击！', 'critical');
            }
        }

        return Math.max(1, damage);
    }

    // 检查战斗是否结束
    checkBattleEnd() {
        const battle = this.currentBattle;

        const alivePlayers = battle.turnOrder.filter(u => u.side === 'player' && u.health > 0);
        const aliveEnemies = battle.turnOrder.filter(u => u.side === 'enemy' && u.health > 0);

        if (alivePlayers.length === 0) {
            // 玩家失败
            setTimeout(() => {
                this.endBattle({ victory: false });
            }, 1500);
            return true;
        } else if (aliveEnemies.length === 0) {
            // 玩家胜利
            setTimeout(() => {
                this.endBattle({ victory: true });
            }, 1500);
            return true;
        } else if (battle.round > 20) {
            // 超过20回合，判定为平局（玩家失败）
            if (window.Game.ui) {
                window.Game.ui.addBattleLog('战斗时间过长，撤退！', 'info');
            }
            setTimeout(() => {
                this.endBattle({ victory: false });
            }, 1500);
            return true;
        }

        return false;
    }

    // 计算战斗结果
    calculateBattleResult() {
        const playerPower = this.calculateTotalPower(this.currentBattle.playerUnits);
        const enemyPower = this.calculateTotalPower(this.currentBattle.enemyUnits);
        
        // 简单的战斗力对比
        const playerAdvantage = playerPower / (playerPower + enemyPower);
        const randomFactor = 0.8 + Math.random() * 0.4; // 0.8-1.2的随机因子
        
        const victory = (playerAdvantage * randomFactor) > 0.5;
        
        // 计算伤亡
        const casualties = this.calculateCasualties(victory);
        
        return {
            victory: victory,
            playerCasualties: casualties.player,
            enemyCasualties: casualties.enemy,
            playerPower: playerPower,
            enemyPower: enemyPower
        };
    }

    // 计算总战斗力
    calculateTotalPower(units) {
        let totalPower = 0;
        
        for (const unit of units) {
            if (unit.health > 0) {
                const attack = unit.getAttack ? unit.getAttack() : unit.attack;
                const defense = unit.getDefense ? unit.getDefense() : unit.defense;
                const healthRatio = unit.health / unit.maxHealth;
                
                totalPower += (attack + defense) * healthRatio;
            }
        }
        
        return totalPower;
    }

    // 计算伤亡
    calculateCasualties(victory) {
        const playerUnits = this.currentBattle.playerUnits;
        const enemyUnits = this.currentBattle.enemyUnits;
        
        let playerCasualties = 0;
        let enemyCasualties = 0;
        
        if (victory) {
            // 玩家胜利，敌军全灭，玩家有少量伤亡
            enemyCasualties = enemyUnits.length;
            playerCasualties = Math.floor(playerUnits.length * (0.1 + Math.random() * 0.2));
        } else {
            // 玩家失败，玩家损失较大
            playerCasualties = Math.floor(playerUnits.length * (0.3 + Math.random() * 0.4));
            enemyCasualties = Math.floor(enemyUnits.length * (0.2 + Math.random() * 0.3));
        }
        
        // 应用伤亡
        this.applyCasualties(playerUnits, playerCasualties);
        this.applyCasualties(enemyUnits, enemyCasualties);
        
        return {
            player: playerCasualties,
            enemy: enemyCasualties
        };
    }

    // 应用伤亡
    applyCasualties(units, casualties) {
        const shuffled = Utils.shuffle([...units]);
        
        for (let i = 0; i < Math.min(casualties, shuffled.length); i++) {
            shuffled[i].health = 0;
        }
        
        // 对剩余单位造成伤害
        for (let i = casualties; i < shuffled.length; i++) {
            const damageRatio = 0.1 + Math.random() * 0.3;
            shuffled[i].health = Math.max(1, Math.floor(shuffled[i].health * (1 - damageRatio)));
        }
    }

    // 结束战斗
    endBattle(result) {
        if (!this.currentBattle) return;

        const battle = this.currentBattle;
        
        if (result.victory) {
            // 玩家胜利
            this.handleVictory(battle);
        } else {
            // 玩家失败
            this.handleDefeat(battle);
        }
        
        // 更新军队状态
        if (window.Game.army) {
            window.Game.army.returnArmy(battle.playerUnits);
        }
        
        // 记录战斗历史
        this.battleHistory.push({
            stageId: battle.stageId,
            victory: result.victory,
            timestamp: Date.now(),
            playerCasualties: result.playerCasualties,
            enemyCasualties: result.enemyCasualties
        });
        
        GameEvents.emit(GAME_EVENTS.BATTLE_END, {
            battle: battle,
            result: result
        });

        // 延迟隐藏战斗界面
        setTimeout(() => {
            if (window.Game.ui) {
                window.Game.ui.hideBattleScreen();
            }
        }, 3000); // 3秒后自动关闭

        this.currentBattle = null;
    }

    // 处理胜利
    handleVictory(battle) {
        const rewards = battle.stageData.rewards;
        
        // 给予奖励
        if (rewards) {
            for (const [resource, amount] of Object.entries(rewards)) {
                if (resource === 'exp') {
                    if (window.Game.gainExp) {
                        window.Game.gainExp(amount);
                    }
                } else if (window.Game.resources) {
                    window.Game.resources.addResource(resource, amount);
                }
            }
        }
        
        GameEvents.emit(GAME_EVENTS.BATTLE_WIN, {
            stageId: battle.stageId,
            rewards: rewards
        });
        
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `战斗胜利！获得奖励：${this.formatRewards(rewards)}`
        });
    }

    // 处理失败
    handleDefeat(battle) {
        GameEvents.emit(GAME_EVENTS.BATTLE_LOSE, {
            stageId: battle.stageId
        });

        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.ERROR,
            text: '战斗失败！请提升实力后再来挑战。'
        });
    }

    // 处理新手训练胜利
    handleTutorialVictory(stageData) {
        const rewards = stageData.rewards;

        // 给予奖励
        if (rewards) {
            for (const [resource, amount] of Object.entries(rewards)) {
                if (resource === 'exp') {
                    if (window.Game.gainExp) {
                        window.Game.gainExp(amount);
                    }
                } else if (window.Game.resources) {
                    window.Game.resources.addResource(resource, amount);
                }
            }
        }

        GameEvents.emit(GAME_EVENTS.BATTLE_WIN, {
            stageId: stageData.id,
            rewards: rewards
        });

        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `新手训练完成！获得奖励：${this.formatRewards(rewards)}`
        });
    }

    // 格式化奖励
    formatRewards(rewards) {
        if (!rewards) return '';
        
        const parts = [];
        for (const [resource, amount] of Object.entries(rewards)) {
            if (resource === 'exp') {
                parts.push(`${amount}经验`);
            } else {
                parts.push(`${amount}${this.getResourceName(resource)}`);
            }
        }
        return parts.join(', ');
    }

    // 获取资源名称
    getResourceName(resource) {
        const names = {
            gold: '金币',
            wood: '木材',
            stone: '石材',
            food: '食物'
        };
        return names[resource] || resource;
    }

    // 获取可用的战斗关卡
    getAvailableStages() {
        return GameData.battles.campaign.filter(stage => 
            window.Game.player.level >= stage.unlockLevel
        );
    }

    // 检查关卡是否解锁
    isStageUnlocked(stageId) {
        const stage = this.getBattleStage(stageId);
        return stage && window.Game.player.level >= stage.unlockLevel;
    }

    // 获取战斗历史
    getBattleHistory() {
        return [...this.battleHistory];
    }

    // 获取胜利次数
    getVictoryCount(stageId = null) {
        if (stageId) {
            return this.battleHistory.filter(battle => 
                battle.stageId === stageId && battle.victory
            ).length;
        } else {
            return this.battleHistory.filter(battle => battle.victory).length;
        }
    }

    // 重置战斗系统
    reset() {
        this.currentBattle = null;
        this.battleHistory = [];
    }

    // 加载战斗数据
    loadData(battleData) {
        if (battleData.battleHistory) {
            this.battleHistory = battleData.battleHistory;
        }
    }

    // 获取保存数据
    getSaveData() {
        return {
            battleHistory: this.battleHistory
        };
    }
}

// 导出战斗系统
window.BattleSystem = BattleSystem;
