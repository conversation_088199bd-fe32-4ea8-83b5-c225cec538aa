// 王国争霸H5 - 资源管理系统

class ResourceManager {
    constructor() {
        this.resources = {
            gold: 1000,
            wood: 500,
            stone: 300,
            food: 800
        };
        
        this.maxResources = {
            gold: 10000,
            wood: 10000,
            stone: 10000,
            food: 10000
        };
        
        this.production = {
            gold: 0,
            wood: 0,
            stone: 0,
            food: 0
        };
        
        this.productionTimer = 0;
        this.productionInterval = 60000; // 60秒生产一次
        
        this.init();
    }

    // 初始化资源管理器
    init() {
        // 监听建筑变化事件
        GameEvents.on(GAME_EVENTS.BUILDING_BUILD, this.updateProduction.bind(this));
        GameEvents.on(GAME_EVENTS.BUILDING_UPGRADE, this.updateProduction.bind(this));
        GameEvents.on(GAME_EVENTS.BUILDING_DESTROY, this.updateProduction.bind(this));
        
        // 监听科技研发完成事件
        GameEvents.on(GAME_EVENTS.TECH_COMPLETE, this.applyTechBonus.bind(this));
        
        this.updateUI();
        console.log('Resource manager initialized');
    }

    // 更新资源系统
    update(deltaTime) {
        this.productionTimer += deltaTime;
        
        if (this.productionTimer >= this.productionInterval) {
            this.produceResources();
            this.productionTimer = 0;
        }
    }

    // 生产资源
    produceResources() {
        let produced = false;
        
        for (const [type, amount] of Object.entries(this.production)) {
            if (amount > 0) {
                const oldAmount = this.resources[type];
                this.addResource(type, amount);
                
                if (this.resources[type] > oldAmount) {
                    produced = true;
                }
            }
        }
        
        if (produced) {
            this.updateUI();
            GameEvents.emit(GAME_EVENTS.RESOURCE_CHANGED, this.resources);
        }
    }

    // 添加资源
    addResource(type, amount) {
        if (!this.resources.hasOwnProperty(type)) {
            console.warn(`Unknown resource type: ${type}`);
            return false;
        }
        
        const oldAmount = this.resources[type];
        this.resources[type] = Math.min(
            this.resources[type] + amount,
            this.maxResources[type]
        );
        
        const actualAdded = this.resources[type] - oldAmount;
        
        if (actualAdded > 0) {
            this.updateUI();
            GameEvents.emit(GAME_EVENTS.RESOURCE_CHANGED, {
                type,
                amount: actualAdded,
                total: this.resources[type]
            });
            
            return true;
        }
        
        return false;
    }

    // 消耗资源
    spendResource(type, amount) {
        if (!this.resources.hasOwnProperty(type)) {
            console.warn(`Unknown resource type: ${type}`);
            return false;
        }
        
        if (this.resources[type] >= amount) {
            this.resources[type] -= amount;
            this.updateUI();
            
            GameEvents.emit(GAME_EVENTS.RESOURCE_CHANGED, {
                type,
                amount: -amount,
                total: this.resources[type]
            });
            
            return true;
        }
        
        GameEvents.emit(GAME_EVENTS.RESOURCE_INSUFFICIENT, { type, required: amount, available: this.resources[type] });
        return false;
    }

    // 批量消耗资源
    spendResources(costs) {
        // 先检查是否有足够的资源
        for (const [type, amount] of Object.entries(costs)) {
            if (!this.hasResource(type, amount)) {
                GameEvents.emit(GAME_EVENTS.RESOURCE_INSUFFICIENT, {
                    type,
                    required: amount,
                    available: this.resources[type]
                });
                return false;
            }
        }
        
        // 消耗资源
        for (const [type, amount] of Object.entries(costs)) {
            this.spendResource(type, amount);
        }
        
        return true;
    }

    // 检查是否有足够的资源
    hasResource(type, amount) {
        return this.resources[type] >= amount;
    }

    // 检查是否有足够的多种资源
    hasResources(costs) {
        for (const [type, amount] of Object.entries(costs)) {
            if (!this.hasResource(type, amount)) {
                return false;
            }
        }
        return true;
    }

    // 获取资源数量
    getResource(type) {
        return this.resources[type] || 0;
    }

    // 获取所有资源
    getAllResources() {
        return { ...this.resources };
    }

    // 设置资源数量
    setResource(type, amount) {
        if (this.resources.hasOwnProperty(type)) {
            this.resources[type] = Math.min(Math.max(amount, 0), this.maxResources[type]);
            this.updateUI();
            GameEvents.emit(GAME_EVENTS.RESOURCE_CHANGED, this.resources);
        }
    }

    // 设置资源上限
    setMaxResource(type, amount) {
        if (this.maxResources.hasOwnProperty(type)) {
            this.maxResources[type] = Math.max(amount, 0);
            // 如果当前资源超过新上限，调整到上限
            if (this.resources[type] > this.maxResources[type]) {
                this.resources[type] = this.maxResources[type];
                this.updateUI();
            }
        }
    }

    // 获取资源上限
    getMaxResource(type) {
        return this.maxResources[type] || 0;
    }

    // 更新生产效率
    updateProduction() {
        // 重置生产值
        this.production = {
            gold: 0,
            wood: 0,
            stone: 0,
            food: 0
        };
        
        // 计算建筑生产
        if (window.Game && window.Game.buildings) {
            const buildings = window.Game.buildings.getAllBuildings();
            
            for (const building of buildings) {
                const buildingData = GameData.buildings[building.type];
                if (buildingData && buildingData.produces) {
                    for (const [resource, amount] of Object.entries(buildingData.produces)) {
                        if (this.production.hasOwnProperty(resource)) {
                            // 考虑建筑等级加成
                            const levelMultiplier = 1 + (building.level - 1) * 0.2;
                            this.production[resource] += amount * levelMultiplier;
                        }
                    }
                }
            }
        }
        
        // 应用科技加成
        this.applyTechBonuses();
        
        console.log('Production updated:', this.production);
    }

    // 应用科技加成
    applyTechBonuses() {
        if (window.Game && window.Game.tech) {
            const techs = window.Game.tech.getResearchedTechs();
            
            for (const techId of techs) {
                const techData = GameData.technologies[techId];
                if (techData && techData.effects) {
                    // 应用生产加成
                    if (techData.effects.foodProduction) {
                        this.production.food *= techData.effects.foodProduction;
                    }
                    if (techData.effects.goldProduction) {
                        this.production.gold *= techData.effects.goldProduction;
                    }
                    if (techData.effects.stoneProduction) {
                        this.production.stone *= techData.effects.stoneProduction;
                    }
                    if (techData.effects.woodProduction) {
                        this.production.wood *= techData.effects.woodProduction;
                    }
                }
            }
        }
    }

    // 应用单个科技加成
    applyTechBonus(techId) {
        this.updateProduction();
    }

    // 获取生产效率
    getProduction(type) {
        return this.production[type] || 0;
    }

    // 获取所有生产效率
    getAllProduction() {
        return { ...this.production };
    }

    // 更新UI显示
    updateUI() {
        Utils.setContent('#goldAmount', Utils.formatNumber(this.resources.gold));
        Utils.setContent('#woodAmount', Utils.formatNumber(this.resources.wood));
        Utils.setContent('#stoneAmount', Utils.formatNumber(this.resources.stone));
        Utils.setContent('#foodAmount', Utils.formatNumber(this.resources.food));
    }

    // 获取资源百分比
    getResourcePercentage(type) {
        return (this.resources[type] / this.maxResources[type]) * 100;
    }

    // 检查资源是否已满
    isResourceFull(type) {
        return this.resources[type] >= this.maxResources[type];
    }

    // 获取资源不足的类型
    getInsufficientResources(costs) {
        const insufficient = {};
        
        for (const [type, amount] of Object.entries(costs)) {
            if (!this.hasResource(type, amount)) {
                insufficient[type] = {
                    required: amount,
                    available: this.resources[type],
                    missing: amount - this.resources[type]
                };
            }
        }
        
        return insufficient;
    }

    // 重置资源（用于新游戏）
    reset() {
        this.resources = {
            gold: 1000,
            wood: 500,
            stone: 300,
            food: 800
        };
        
        this.production = {
            gold: 0,
            wood: 0,
            stone: 0,
            food: 0
        };
        
        this.productionTimer = 0;
        this.updateUI();
    }

    // 加载资源数据
    loadData(data) {
        if (data.resources) {
            this.resources = { ...data.resources };
        }
        if (data.maxResources) {
            this.maxResources = { ...data.maxResources };
        }
        if (data.production) {
            this.production = { ...data.production };
        }
        
        this.updateUI();
    }

    // 获取保存数据
    getSaveData() {
        return {
            resources: { ...this.resources },
            maxResources: { ...this.maxResources },
            production: { ...this.production },
            productionTimer: this.productionTimer
        };
    }
}

// 导出资源管理器
window.ResourceManager = ResourceManager;
