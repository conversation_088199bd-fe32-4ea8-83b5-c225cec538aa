// 王国争霸H5 - 军队系统

class Unit {
    constructor(type) {
        this.id = Utils.generateId();
        this.type = type;
        this.data = GameData.units[type];
        
        if (!this.data) {
            throw new Error(`Unknown unit type: ${type}`);
        }
        
        this.level = 1;
        this.health = this.data.health;
        this.maxHealth = this.data.health;
        this.isTraining = false;
        this.trainTimeRemaining = 0;
    }

    // 开始训练
    startTraining() {
        this.isTraining = true;
        this.trainTimeRemaining = this.data.trainTime * 1000;
    }

    // 更新单位状态
    update(deltaTime) {
        if (this.isTraining) {
            this.trainTimeRemaining -= deltaTime;
            
            if (this.trainTimeRemaining <= 0) {
                this.completeTraining();
            }
        }
    }

    // 完成训练
    completeTraining() {
        this.isTraining = false;
        this.trainTimeRemaining = 0;
        
        GameEvents.emit(GAME_EVENTS.ARMY_TRAIN_COMPLETE, this);
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.SUCCESS,
            text: `${this.data.name}训练完成！`
        });
    }

    // 获取攻击力
    getAttack() {
        let attack = this.data.attack;
        
        // 应用科技加成
        if (window.Game && window.Game.tech) {
            const techs = window.Game.tech.getResearchedTechs();
            for (const techId of techs) {
                const techData = GameData.technologies[techId];
                if (techData && techData.effects && techData.effects.unitAttack) {
                    attack *= techData.effects.unitAttack;
                }
            }
        }
        
        return Math.floor(attack);
    }

    // 获取防御力
    getDefense() {
        let defense = this.data.defense;
        
        // 应用科技加成
        if (window.Game && window.Game.tech) {
            const techs = window.Game.tech.getResearchedTechs();
            for (const techId of techs) {
                const techData = GameData.technologies[techId];
                if (techData && techData.effects && techData.effects.unitDefense) {
                    defense *= techData.effects.unitDefense;
                }
            }
        }
        
        return Math.floor(defense);
    }

    // 获取移动速度
    getSpeed() {
        let speed = this.data.speed;
        
        // 应用科技加成
        if (window.Game && window.Game.tech) {
            const techs = window.Game.tech.getResearchedTechs();
            for (const techId of techs) {
                const techData = GameData.technologies[techId];
                if (techData && techData.effects && techData.effects.unitSpeed) {
                    speed *= techData.effects.unitSpeed;
                }
            }
        }
        
        return speed;
    }

    // 获取单位信息
    getInfo() {
        return {
            id: this.id,
            type: this.type,
            name: this.data.name,
            level: this.level,
            health: this.health,
            maxHealth: this.maxHealth,
            attack: this.getAttack(),
            defense: this.getDefense(),
            speed: this.getSpeed(),
            isTraining: this.isTraining,
            trainTimeRemaining: this.trainTimeRemaining
        };
    }
}

class ArmySystem {
    constructor() {
        this.units = new Map();
        this.trainingQueue = [];
        this.maxArmySize = 50;
        
        this.init();
    }

    // 初始化军队系统
    init() {
        console.log('Army system initialized');
    }

    // 更新军队系统
    update(deltaTime) {
        // 更新所有单位
        for (const unit of this.units.values()) {
            unit.update(deltaTime);
        }
        
        // 处理训练队列
        this.processTrainingQueue();
    }

    // 训练单位
    trainUnit(type, count = 1) {
        const unitData = GameData.units[type];
        if (!unitData) {
            console.error(`Unknown unit type: ${type}`);
            return false;
        }

        // 检查等级要求
        if (window.Game.player.level < unitData.unlockLevel) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: `需要等级${unitData.unlockLevel}才能训练${unitData.name}！`
            });
            return false;
        }

        // 检查是否有对应的训练建筑
        if (unitData.requiredBuilding) {
            const buildingCount = window.Game.buildings.getBuildingCount(unitData.requiredBuilding);
            if (buildingCount === 0) {
                const buildingName = GameData.buildings[unitData.requiredBuilding]?.name || unitData.requiredBuilding;
                GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                    type: MESSAGE_TYPES.WARNING,
                    text: `需要建造${buildingName}才能训练${unitData.name}！`
                });
                return false;
            }
        }

        // 检查人口限制
        const currentPopulation = this.getCurrentPopulation();
        const requiredPopulation = unitData.population * count;
        const maxPopulation = window.Game.getMaxPopulation();
        
        if (currentPopulation + requiredPopulation > maxPopulation) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '人口不足，无法训练更多军队！'
            });
            return false;
        }

        // 检查军队数量限制
        if (this.units.size + count > this.maxArmySize) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '军队数量已达上限！'
            });
            return false;
        }

        // 计算总成本
        const totalCost = {};
        for (const [resource, amount] of Object.entries(unitData.cost)) {
            totalCost[resource] = amount * count;
        }

        // 检查资源
        if (!window.Game.resources.hasResources(totalCost)) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.ERROR,
                text: '资源不足，无法训练军队！'
            });
            return false;
        }

        // 消耗资源
        window.Game.resources.spendResources(totalCost);

        // 创建单位并开始训练
        for (let i = 0; i < count; i++) {
            const unit = new Unit(type);
            unit.startTraining();
            this.units.set(unit.id, unit);
        }

        GameEvents.emit(GAME_EVENTS.ARMY_RECRUIT, { type, count });
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.INFO,
            text: `开始训练${count}个${unitData.name}...`
        });

        return true;
    }

    // 处理训练队列
    processTrainingQueue() {
        // 这里可以实现更复杂的训练队列逻辑
        // 目前所有单位同时训练
    }

    // 获取当前人口
    getCurrentPopulation() {
        let population = 0;
        
        for (const unit of this.units.values()) {
            if (!unit.isTraining) {
                population += unit.data.population;
            }
        }
        
        return population;
    }

    // 获取指定类型的单位数量
    getUnitCount(type) {
        let count = 0;
        
        for (const unit of this.units.values()) {
            if (unit.type === type && !unit.isTraining) {
                count++;
            }
        }
        
        return count;
    }

    // 获取训练中的单位数量
    getTrainingCount(type = null) {
        let count = 0;
        
        for (const unit of this.units.values()) {
            if (unit.isTraining && (type === null || unit.type === type)) {
                count++;
            }
        }
        
        return count;
    }

    // 获取所有单位
    getAllUnits() {
        return Array.from(this.units.values());
    }

    // 获取可用的单位（训练完成的）
    getAvailableUnits() {
        return Array.from(this.units.values()).filter(unit => !unit.isTraining);
    }

    // 解散单位
    dismissUnit(unitId) {
        const unit = this.units.get(unitId);
        if (!unit) return false;

        if (unit.isTraining) {
            GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                type: MESSAGE_TYPES.WARNING,
                text: '无法解散训练中的单位！'
            });
            return false;
        }

        this.units.delete(unitId);
        
        GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
            type: MESSAGE_TYPES.INFO,
            text: `${unit.data.name}已解散`
        });

        return true;
    }

    // 获取军队战斗力
    getTotalPower() {
        let power = 0;
        
        for (const unit of this.units.values()) {
            if (!unit.isTraining) {
                power += unit.getAttack() + unit.getDefense();
            }
        }
        
        return power;
    }

    // 部署军队（用于战斗）
    deployArmy(unitIds) {
        const deployedUnits = [];
        
        for (const unitId of unitIds) {
            const unit = this.units.get(unitId);
            if (unit && !unit.isTraining) {
                deployedUnits.push(unit);
            }
        }
        
        GameEvents.emit(GAME_EVENTS.ARMY_DEPLOY, deployedUnits);
        return deployedUnits;
    }

    // 军队返回
    returnArmy(units) {
        // 处理战斗后的军队状态
        for (const unit of units) {
            if (unit.health <= 0) {
                // 单位死亡，从军队中移除
                this.units.delete(unit.id);
            }
        }
        
        GameEvents.emit(GAME_EVENTS.ARMY_RETURN, units);
    }

    // 治疗军队
    healArmy() {
        let healCost = 0;
        let healedCount = 0;
        
        for (const unit of this.units.values()) {
            if (!unit.isTraining && unit.health < unit.maxHealth) {
                const healAmount = unit.maxHealth - unit.health;
                healCost += Math.floor(healAmount * 0.1); // 治疗成本
                unit.health = unit.maxHealth;
                healedCount++;
            }
        }
        
        if (healedCount > 0) {
            if (window.Game.resources.hasResource('gold', healCost)) {
                window.Game.resources.spendResource('gold', healCost);
                GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                    type: MESSAGE_TYPES.SUCCESS,
                    text: `治疗了${healedCount}个单位，花费${healCost}金币`
                });
            } else {
                GameEvents.emit(GAME_EVENTS.UI_MESSAGE, {
                    type: MESSAGE_TYPES.ERROR,
                    text: `治疗需要${healCost}金币！`
                });
            }
        }
    }

    // 重置军队系统
    reset() {
        this.units.clear();
        this.trainingQueue = [];
    }

    // 加载军队数据
    loadData(unitsData) {
        this.reset();
        
        for (const data of unitsData) {
            const unit = new Unit(data.type);
            unit.level = data.level || 1;
            unit.health = data.health || unit.maxHealth;
            unit.isTraining = data.isTraining || false;
            unit.trainTimeRemaining = data.trainTimeRemaining || 0;
            
            this.units.set(unit.id, unit);
        }
    }

    // 获取保存数据
    getSaveData() {
        const unitsData = [];
        
        for (const unit of this.units.values()) {
            unitsData.push({
                type: unit.type,
                level: unit.level,
                health: unit.health,
                isTraining: unit.isTraining,
                trainTimeRemaining: unit.trainTimeRemaining
            });
        }
        
        return unitsData;
    }
}

// 导出军队系统
window.Unit = Unit;
window.ArmySystem = ArmySystem;
