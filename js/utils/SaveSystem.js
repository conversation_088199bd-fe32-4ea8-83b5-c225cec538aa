// 王国争霸H5 - 存档系统

class SaveSystem {
    constructor() {
        this.saveKey = 'kingdom_battle_save';
        this.settingsKey = 'kingdom_battle_settings';
        this.autoSaveInterval = 60000; // 60秒自动保存
        this.autoSaveTimer = null;
    }

    // 初始化存档系统
    init() {
        this.startAutoSave();
        
        // 监听页面关闭事件，自动保存
        window.addEventListener('beforeunload', () => {
            this.saveGame();
        });
    }

    // 保存游戏数据
    saveGame(gameData = null) {
        try {
            if (!gameData && window.Game) {
                gameData = this.getGameData();
            }
            
            if (!gameData) {
                console.warn('No game data to save');
                return false;
            }

            const saveData = {
                version: '1.0.0',
                timestamp: Date.now(),
                gameData: gameData
            };

            Utils.setStorage(this.saveKey, saveData);
            GameEvents.emit(GAME_EVENTS.GAME_SAVE, saveData);
            
            console.log('Game saved successfully');
            return true;
        } catch (error) {
            console.error('Failed to save game:', error);
            GameEvents.emit(GAME_EVENTS.ERROR, 'Failed to save game');
            return false;
        }
    }

    // 加载游戏数据
    loadGame() {
        try {
            const saveData = Utils.getStorage(this.saveKey);
            
            if (!saveData) {
                console.log('No save data found');
                return null;
            }

            // 检查版本兼容性
            if (!this.isVersionCompatible(saveData.version)) {
                console.warn('Save data version incompatible');
                return null;
            }

            GameEvents.emit(GAME_EVENTS.GAME_LOAD, saveData.gameData);
            console.log('Game loaded successfully');
            
            return saveData.gameData;
        } catch (error) {
            console.error('Failed to load game:', error);
            GameEvents.emit(GAME_EVENTS.ERROR, 'Failed to load game');
            return null;
        }
    }

    // 检查是否有存档
    hasSaveData() {
        const saveData = Utils.getStorage(this.saveKey);
        return saveData !== null;
    }

    // 删除存档
    deleteSave() {
        try {
            Utils.removeStorage(this.saveKey);
            console.log('Save data deleted');
            return true;
        } catch (error) {
            console.error('Failed to delete save:', error);
            return false;
        }
    }

    // 获取存档信息
    getSaveInfo() {
        const saveData = Utils.getStorage(this.saveKey);
        if (!saveData) {
            return null;
        }

        return {
            version: saveData.version,
            timestamp: saveData.timestamp,
            date: new Date(saveData.timestamp).toLocaleString(),
            playerLevel: saveData.gameData?.player?.level || 1,
            playtime: saveData.gameData?.playtime || 0
        };
    }

    // 保存设置
    saveSettings(settings) {
        try {
            Utils.setStorage(this.settingsKey, settings);
            return true;
        } catch (error) {
            console.error('Failed to save settings:', error);
            return false;
        }
    }

    // 加载设置
    loadSettings() {
        return Utils.getStorage(this.settingsKey, this.getDefaultSettings());
    }

    // 获取默认设置
    getDefaultSettings() {
        return {
            soundEnabled: true,
            musicEnabled: true,
            soundVolume: 0.7,
            musicVolume: 0.5,
            autoSave: true,
            language: 'zh-CN'
        };
    }

    // 开始自动保存
    startAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }

        this.autoSaveTimer = setInterval(() => {
            if (window.Game && window.Game.isRunning) {
                this.saveGame();
            }
        }, this.autoSaveInterval);
    }

    // 停止自动保存
    stopAutoSave() {
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
            this.autoSaveTimer = null;
        }
    }

    // 检查版本兼容性
    isVersionCompatible(version) {
        // 简单的版本检查，实际项目中可能需要更复杂的逻辑
        const currentVersion = '1.0.0';
        return version === currentVersion;
    }

    // 获取游戏数据
    getGameData() {
        if (!window.Game) {
            return null;
        }

        return {
            player: {
                level: Game.player.level,
                exp: Game.player.exp,
                expToNext: Game.player.expToNext
            },
            resources: Game.resources ? Game.resources.getSaveData() : null,
            buildings: Game.buildings ? Game.buildings.getSaveData() : [],
            army: Game.army ? Game.army.getSaveData() : [],
            technologies: Game.tech ? Game.tech.getSaveData() : null,
            quests: Game.quests ? Game.quests.getSaveData() : null,
            battle: Game.battle ? Game.battle.getSaveData() : null,
            playtime: Game.playtime || 0,
            lastSave: Date.now()
        };
    }

    // 导出存档（用于分享或备份）
    exportSave() {
        const saveData = Utils.getStorage(this.saveKey);
        if (!saveData) {
            return null;
        }

        try {
            const exportData = btoa(JSON.stringify(saveData));
            return exportData;
        } catch (error) {
            console.error('Failed to export save:', error);
            return null;
        }
    }

    // 导入存档
    importSave(exportData) {
        try {
            const saveData = JSON.parse(atob(exportData));
            
            if (!this.isVersionCompatible(saveData.version)) {
                throw new Error('Incompatible save version');
            }

            Utils.setStorage(this.saveKey, saveData);
            return true;
        } catch (error) {
            console.error('Failed to import save:', error);
            return false;
        }
    }

    // 创建新游戏存档
    createNewGame() {
        const newGameData = {
            player: {
                level: 1,
                exp: 0,
                expToNext: 100
            },
            resources: {
                gold: 1000,
                wood: 500,
                stone: 300,
                food: 800
            },
            buildings: [],
            army: [],
            technologies: [],
            quests: [],
            achievements: [],
            playtime: 0,
            lastSave: Date.now()
        };

        return this.saveGame(newGameData);
    }

    // 获取存档大小（字节）
    getSaveSize() {
        const saveData = Utils.getStorage(this.saveKey);
        if (!saveData) {
            return 0;
        }
        
        return new Blob([JSON.stringify(saveData)]).size;
    }

    // 清理旧存档（如果需要）
    cleanup() {
        // 可以在这里实现清理逻辑，比如删除过期的存档
        console.log('Save system cleanup completed');
    }
}

// 创建全局存档系统实例
const SaveManager = new SaveSystem();

// 导出
window.SaveSystem = SaveSystem;
window.SaveManager = SaveManager;
