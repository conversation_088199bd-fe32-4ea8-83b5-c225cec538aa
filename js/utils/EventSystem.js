// 王国争霸H5 - 事件系统

class EventSystem {
    constructor() {
        this.listeners = new Map();
    }

    // 添加事件监听器
    on(eventName, callback, context = null) {
        if (!this.listeners.has(eventName)) {
            this.listeners.set(eventName, []);
        }
        
        this.listeners.get(eventName).push({
            callback,
            context,
            once: false
        });
    }

    // 添加一次性事件监听器
    once(eventName, callback, context = null) {
        if (!this.listeners.has(eventName)) {
            this.listeners.set(eventName, []);
        }
        
        this.listeners.get(eventName).push({
            callback,
            context,
            once: true
        });
    }

    // 移除事件监听器
    off(eventName, callback = null, context = null) {
        if (!this.listeners.has(eventName)) {
            return;
        }

        const listeners = this.listeners.get(eventName);
        
        if (callback === null) {
            // 移除所有监听器
            this.listeners.delete(eventName);
        } else {
            // 移除特定监听器
            for (let i = listeners.length - 1; i >= 0; i--) {
                const listener = listeners[i];
                if (listener.callback === callback && 
                    (context === null || listener.context === context)) {
                    listeners.splice(i, 1);
                }
            }
            
            if (listeners.length === 0) {
                this.listeners.delete(eventName);
            }
        }
    }

    // 触发事件
    emit(eventName, ...args) {
        if (!this.listeners.has(eventName)) {
            return;
        }

        const listeners = this.listeners.get(eventName);
        const toRemove = [];

        for (let i = 0; i < listeners.length; i++) {
            const listener = listeners[i];
            
            try {
                if (listener.context) {
                    listener.callback.call(listener.context, ...args);
                } else {
                    listener.callback(...args);
                }
            } catch (error) {
                console.error(`Error in event listener for ${eventName}:`, error);
            }

            if (listener.once) {
                toRemove.push(i);
            }
        }

        // 移除一次性监听器
        for (let i = toRemove.length - 1; i >= 0; i--) {
            listeners.splice(toRemove[i], 1);
        }

        if (listeners.length === 0) {
            this.listeners.delete(eventName);
        }
    }

    // 检查是否有监听器
    hasListeners(eventName) {
        return this.listeners.has(eventName) && this.listeners.get(eventName).length > 0;
    }

    // 获取监听器数量
    getListenerCount(eventName) {
        return this.listeners.has(eventName) ? this.listeners.get(eventName).length : 0;
    }

    // 清除所有监听器
    clear() {
        this.listeners.clear();
    }

    // 获取所有事件名称
    getEventNames() {
        return Array.from(this.listeners.keys());
    }
}

// 全局事件系统实例
const GameEvents = new EventSystem();

// 游戏事件常量
const GAME_EVENTS = {
    // 游戏状态
    GAME_START: 'game_start',
    GAME_PAUSE: 'game_pause',
    GAME_RESUME: 'game_resume',
    GAME_SAVE: 'game_save',
    GAME_LOAD: 'game_load',
    
    // 资源相关
    RESOURCE_CHANGED: 'resource_changed',
    RESOURCE_INSUFFICIENT: 'resource_insufficient',
    
    // 建筑相关
    BUILDING_BUILD: 'building_build',
    BUILDING_UPGRADE: 'building_upgrade',
    BUILDING_COMPLETE: 'building_complete',
    BUILDING_DESTROY: 'building_destroy',
    BUILDING_SELECT: 'building_select',
    
    // 军队相关
    ARMY_RECRUIT: 'army_recruit',
    ARMY_TRAIN_COMPLETE: 'army_train_complete',
    ARMY_DEPLOY: 'army_deploy',
    ARMY_RETURN: 'army_return',
    
    // 科技相关
    TECH_RESEARCH: 'tech_research',
    TECH_COMPLETE: 'tech_complete',
    TECH_UNLOCK: 'tech_unlock',
    
    // 战斗相关
    BATTLE_START: 'battle_start',
    BATTLE_END: 'battle_end',
    BATTLE_WIN: 'battle_win',
    BATTLE_LOSE: 'battle_lose',
    
    // 任务相关
    QUEST_START: 'quest_start',
    QUEST_COMPLETE: 'quest_complete',
    QUEST_FAIL: 'quest_fail',
    QUEST_UPDATE: 'quest_update',
    
    // 等级相关
    LEVEL_UP: 'level_up',
    EXP_GAIN: 'exp_gain',
    
    // UI相关
    UI_PANEL_OPEN: 'ui_panel_open',
    UI_PANEL_CLOSE: 'ui_panel_close',
    UI_MESSAGE: 'ui_message',
    UI_CONFIRM: 'ui_confirm',
    
    // 时间相关
    TIME_TICK: 'time_tick',
    TIME_MINUTE: 'time_minute',
    TIME_HOUR: 'time_hour',
    
    // 成就相关
    ACHIEVEMENT_UNLOCK: 'achievement_unlock',
    
    // 错误相关
    ERROR: 'error',
    WARNING: 'warning'
};

// 消息类型
const MESSAGE_TYPES = {
    SUCCESS: 'success',
    ERROR: 'error',
    INFO: 'info',
    WARNING: 'warning'
};

// 导出
window.EventSystem = EventSystem;
window.GameEvents = GameEvents;
window.GAME_EVENTS = GAME_EVENTS;
window.MESSAGE_TYPES = MESSAGE_TYPES;
