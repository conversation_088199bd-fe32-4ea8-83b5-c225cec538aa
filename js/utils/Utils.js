// 王国争霸H5 - 工具函数库

class Utils {
    // 格式化数字显示
    static formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    // 格式化时间显示
    static formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    // 生成随机ID
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 深拷贝对象
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => Utils.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = Utils.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }

    // 获取元素
    static $(selector) {
        return document.querySelector(selector);
    }

    // 获取所有元素
    static $$(selector) {
        return document.querySelectorAll(selector);
    }

    // 添加事件监听
    static on(element, event, handler) {
        if (typeof element === 'string') {
            element = Utils.$(element);
        }
        if (element) {
            element.addEventListener(event, handler);
        }
    }

    // 移除事件监听
    static off(element, event, handler) {
        if (typeof element === 'string') {
            element = Utils.$(element);
        }
        if (element) {
            element.removeEventListener(event, handler);
        }
    }

    // 显示元素
    static show(element) {
        if (typeof element === 'string') {
            element = Utils.$(element);
        }
        if (element) {
            element.classList.remove('hidden');
        }
    }

    // 隐藏元素
    static hide(element) {
        if (typeof element === 'string') {
            element = Utils.$(element);
        }
        if (element) {
            element.classList.add('hidden');
        }
    }

    // 切换元素显示状态
    static toggle(element) {
        if (typeof element === 'string') {
            element = Utils.$(element);
        }
        if (element) {
            element.classList.toggle('hidden');
        }
    }

    // 添加CSS类
    static addClass(element, className) {
        if (typeof element === 'string') {
            element = Utils.$(element);
        }
        if (element) {
            element.classList.add(className);
        }
    }

    // 移除CSS类
    static removeClass(element, className) {
        if (typeof element === 'string') {
            element = Utils.$(element);
        }
        if (element) {
            element.classList.remove(className);
        }
    }

    // 检查是否有CSS类
    static hasClass(element, className) {
        if (typeof element === 'string') {
            element = Utils.$(element);
        }
        return element ? element.classList.contains(className) : false;
    }

    // 设置元素内容
    static setContent(element, content) {
        if (typeof element === 'string') {
            element = Utils.$(element);
        }
        if (element) {
            element.textContent = content;
        }
    }

    // 设置元素HTML
    static setHTML(element, html) {
        if (typeof element === 'string') {
            element = Utils.$(element);
        }
        if (element) {
            element.innerHTML = html;
        }
    }

    // 创建元素
    static createElement(tag, className = '', content = '') {
        const element = document.createElement(tag);
        if (className) {
            element.className = className;
        }
        if (content) {
            element.textContent = content;
        }
        return element;
    }

    // 延迟执行
    static delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 限制数值范围
    static clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    }

    // 线性插值
    static lerp(start, end, factor) {
        return start + (end - start) * factor;
    }

    // 随机整数
    static randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // 随机浮点数
    static randomFloat(min, max) {
        return Math.random() * (max - min) + min;
    }

    // 随机选择数组元素
    static randomChoice(array) {
        return array[Math.floor(Math.random() * array.length)];
    }

    // 打乱数组
    static shuffle(array) {
        const result = [...array];
        for (let i = result.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [result[i], result[j]] = [result[j], result[i]];
        }
        return result;
    }

    // 检查是否为移动设备
    static isMobile() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    // 获取屏幕尺寸
    static getScreenSize() {
        return {
            width: window.innerWidth,
            height: window.innerHeight
        };
    }

    // 本地存储
    static setStorage(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (e) {
            console.error('Storage error:', e);
            return false;
        }
    }

    static getStorage(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (e) {
            console.error('Storage error:', e);
            return defaultValue;
        }
    }

    static removeStorage(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (e) {
            console.error('Storage error:', e);
            return false;
        }
    }

    // 防抖函数
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 计算两点距离
    static distance(x1, y1, x2, y2) {
        return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    }

    // 角度转弧度
    static toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }

    // 弧度转角度
    static toDegrees(radians) {
        return radians * (180 / Math.PI);
    }
}

// 导出工具类
window.Utils = Utils;
